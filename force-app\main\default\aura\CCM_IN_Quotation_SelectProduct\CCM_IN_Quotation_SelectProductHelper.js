({
	generateCondtion: function (component, brandName) {
		component.set("v.isBusy", true);
		var prodIdCondition = "";
		var action = component.get("c.getPriceBookEntryCondition");
		action.setParams({
			"brandName": brandName,
			"customerId": component.get("v.customerId"),
			"isDropShip": component.get("v.isDropShip")
		});
		action.setCallback(this, function (response) {
			var state = response.getState();
			console.log("state--->" + state);
			if (state === "SUCCESS") {
				var results = JSON.parse(response.getReturnValue());
				var brands = [];
				if (brandName != "" && brandName != null && brandName != undefined) {
					brands = brandName.split("&");
				}
				var defaultCondition = "";
				var isHistoryProduct = $A.get("$Label.c.Is_History_Product");
				if (results.OrgCode === "CCA") {
					defaultCondition =
						'[{"Value":"true","FieldName":"IsActive","Condtion":"="},{"value":"PIM","FieldName":"Source__c","Condtion":"="},{"value":"Parts","FieldName":"RecordType.DeveloperName","Condtion":"!="},{"Value":"(\'{1}\')","FieldName":"Brand_Name__c","Condtion":"IN"},{"value":"(\'CS\')","FieldName":"FilterType__c","Condtion":"NOT IN"},{"value":"({2})","FieldName":"Id","Condtion":"IN"},{"Value":"true","FieldName":"Sellable_CCA__c","Condtion":"="},{"Value":"' +
						isHistoryProduct +
						'","FieldName":"Is_History_Product__c","Condtion":"="}]';
				} else {
					defaultCondition =
						'[{"Value":"true","FieldName":"IsActive","Condtion":"="},{"value":"PIM","FieldName":"Source__c","Condtion":"="},{"value":"Parts","FieldName":"RecordType.DeveloperName","Condtion":"!="},{"Value":"(\'{1}\')","FieldName":"Brand_Name__c","Condtion":"IN"},{"value":"(\'CS\')","FieldName":"FilterType__c","Condtion":"NOT IN"},{"value":"({2})","FieldName":"Id","Condtion":"IN"},{"Value":"true","FieldName":"Sellable__c","Condtion":"="},{"Value":"' +
						isHistoryProduct +
						'","FieldName":"Is_History_Product__c","Condtion":"="}]';
				}
				defaultCondition = defaultCondition.replace("{1}", brands.join("','"));
				defaultCondition = defaultCondition.replace("{2}", results.sqlString);

				component.set("v.prodCondition", defaultCondition);
			} else {
				var errors = response.getError();
				if (errors) {
					if (errors[0] && errors[0].message) {
						component.set("v.isBusy", false);
						alert("ERROR: " + errors[0].message);
					}
				} else {
					component.set("v.isBusy", false);
					alert("ERROR: Unknown error");
				}
			}
			component.set("v.isBusy", false);
		});
		$A.enqueueAction(action);

		component.set("v.isBusy", false);
	},
	doSaveAction: function (component, event, isSave) {
		component.set("v.isBusy", true);
		var result = Validator.pass(component.find("required-product"));
		if (!result) {
			component.set("v.isBusy", false);
			return;
        }
        var expectDate = component.get("v.quotation.Expected_Delivery_Date__c");
        var minSelectDate = component.get("v.minSelectDate");
        if (expectDate < minSelectDate) {
            component.set("v.isBusy", false);
        	 return;
        }

		var quotationItemList = JSON.stringify(component.get("v.quotationItemList"));
		if (quotationItemList === "[]" || quotationItemList === undefined) {
			component.set("v.isBusy", false);
			var toastEvent = $A.get("e.force:showToast");
			toastEvent.setParams({
				"title": "Warning!",
				"message": "Please add the product first.",
				"type": "Warning"
			});
			toastEvent.fire();
			return;
		}

		var qList = component.get("v.quotationItemList");
		qList.forEach(function (qItem) {
			if (!qItem.Promotion__c) {
				qItem.PromotionName__c = "";
			}
		});
		component.set("v.quotationItemList", qList);

		//check Mix&Match Order
		this.checkMixMatchOrder(component);

		if (component.get("v.needResearch") == true) {
			var paymentTermVal = component.get("v.paymentTermValue");
			console.log("paymentTermValue validate--->" + paymentTermVal);
			if (!paymentTermVal) {
				component.set("v.isBusy", false);
				var toastEvent = $A.get("e.force:showToast");
				toastEvent.setParams({
					"title": "Warning!",
					"message": $A.get("$Label.c.CCM_Payment_Term_Selection_Reminder"),
					"type": "Warning"
				});
				toastEvent.fire();
				return;
			}
		}

		if (!this.isAllMeetThreshold(component)) {
			component.set("v.isBusy", false);
			var toastEvent = $A.get("e.force:showToast");
			toastEvent.setParams({
				"title": "Warning!",
				"message": "There are order items that do not meet its promotion rules.",
				"type": "Warning"
			});
			toastEvent.fire();
			return;
		}
		// var quotaList = component.get('v.quotationItemList');
		// if (quotaList && quotaList.length > 0) {
		//     var hasLanuchDateGreaterShipDate = false;
		//     quotaList.forEach(function (qItem) {
		//         if (qItem.Ship_Date__c < qItem.Lanch_Date__c) {
		//             hasLanuchDateGreaterShipDate = true;
		//         }
		//     });
		//     if(hasLanuchDateGreaterShipDate){
		//         component.set("v.isBusy", false);
		//         return false;
		//     }
        // }

        var ridingMowerNum = 0;
        var orgcode = component.get("v.customerOrgCode");
        var labelValue = $A.get("$Label.c.CCM_Riding_Mower");
        var ridingMowerArray = labelValue.split(";");

        var labelValue1 = $A.get("$Label.c.CCM_FreightFee600");
        var accountArray = labelValue1.split(";");
        var quotaList = component.get('v.quotationItemList');
        if (quotaList && quotaList.length > 0) {
            quotaList.forEach(function (qItem) {
                if (ridingMowerArray.indexOf(qItem.ProductCode__c) > -1) {
                    ridingMowerNum = ridingMowerNum + qItem.Quantity__c;
                }
            });
        }
        if (orgcode == "CCA" && ridingMowerNum > 0 && ridingMowerNum < 3 && accountArray.indexOf( component.get("v.customerId")) < 0) {
            var toastEvent = $A.get("e.force:showToast");
				toastEvent.setParams({
					"title": "Warning!",
					"message": $A.get("$Label.c.CCM_Riding_Mower_Warnning"),
                    "type": "Warning",
                    "mode": "sticky"
				});
				toastEvent.fire();
        }

		var quotation = component.get("v.quotation");
		quotation.Customer__c = component.get("v.customerId");
		quotation.Brand_Scope__c = component.get("v.brandScope");
		quotation.Is_Delegate__c = true;
		if (!!component.get("v.paymentTermValue")) {
			quotation.Payment_Term__c = component.get("v.paymentTermValue");
		}
		if (component.get("v.termsPromo")) {
			quotation.Payment_Term__c = component.get("v.termsPromoPTValue");
		}

		var termsPromo = component.get("v.termsPromo");
		if (termsPromo) {
			quotation.Payment_Term_Promotion__c = termsPromo.promotion.Id;
			quotation.Payment_Term_Promotion_Window__c = termsPromo.windowId;
		} else {
			quotation.Payment_Term_Promotion__c = null;
			quotation.Payment_Term_Promotion_Window__c = null;
		}

		var wholeOrderPromo = component.get("v.wholeOrderPromo");
		if (wholeOrderPromo) {
			quotation.Whole_Order_Promotion__c = wholeOrderPromo.promotion.Id;
			quotation.Whole_Order_Promotion_Window__c = wholeOrderPromo.windowId;
		} else {
			quotation.Whole_Order_Promotion__c = null;
			quotation.Whole_Order_Promotion_Window__c = null;
		}

		var orderType = component.get("v.orderTypeVal");
		console.log("orderType 2--->" + orderType);
		if (orderType == "Y") {
			quotation.Is_DropShip__c = true;
		} else {
			quotation.Is_DropShip__c = false;
		}
		if (quotation.Purchase_Order_Items__r) {
			quotation.Purchase_Order_Items__r = undefined;
		}
		if (quotation.Customer__r) {
			quotation.Customer__r = undefined;
		}

		let tempItemList = JSON.parse(JSON.stringify(component.get("v.quotationItemList")));
		let quotationItemListParam = [];
		let attributeList = [
			"Product__c",
			"Quantity__c",
			"Brand__c",
			"Ship_Date__c",
			"ProductCode__c",
			"Price_Book__c",
			"List_Price__c",
			"Unit_Price__c",
			"Discount_Amount__c",
			"Sub_Total__c",
			"OverSize__c",
			"Gross_Weight__c",
			"Promotion__c",
			"PromotionName__c",
			"Promotion_Rule_Name__c",
			"Whole_Order_Promotion__c",
			"Promo_Discount_Amount__c",
			"Whole_Order_Promo_Discount_Amount__c",
			"Is_Initial__c",
			"Regular_Promotion_Window__c",
			"Lanch_Date__c"
		];
		tempItemList.forEach((item) => {
			let quotationItemParam = {};
			attributeList.forEach((attr) => {
				if (attr in item) {
					quotationItemParam[attr] = item[attr];
				}
			});
			quotationItemListParam.push(quotationItemParam);
		});

		var action = component.get("c.saveData");
		action.setParams({
			"poString": JSON.stringify(quotation),
			"poItemString": JSON.stringify(quotationItemListParam),
			"currentStep": component.get("v.currentStep"),
			"isDelegate": true
		});
		action.setCallback(this, function (response) {
			var state = response.getState();
			console.log("state--->" + state);
			if (state === "SUCCESS") {
				var results = response.getReturnValue();
				if (results != null && results != undefined) {
					var data = JSON.parse(results);
					if (data != null && data != undefined) {
						component.set("v.quotation", data.po);
						this.calculateTotal(component);
						component.set("v.paymentTermValue", data.po.Payment_Term__c);
						//Populate record Id In quotationItemList
						var qItemList = component.get("v.quotationItemList");
						var index = 0;
						for (; index < data.poItems.length; index++) {
							qItemList[index].Id = data.poItems[index].Id;
						}
						component.set("v.quotationItemList", qItemList);
						//component.set('v.quotationItemList', data.poItems);
						component.set("v.recordId", data.po.Id);

						var toastEvent = $A.get("e.force:showToast");
						toastEvent.setParams({
							"title": "Success!",
							"message": "Saved.",
							"type": "success"
						});
						toastEvent.fire();

						if (isSave == false) {
							var currentStep = component.get("v.currentStep");
							console.log("current step--->" + component.get("v.currentStep"));
							component.set("v.currentStep", currentStep + 1);
						}
					}
				}
			} else {
				var errors = response.getError();
				if (errors) {
					if (errors[0] && errors[0].message) {
						console.log("error--->" + errors[0].message);
					}
				} else {
					console.log("ERROR: Unknown error");
				}
			}
			component.set("v.isBusy", false);
		});
		$A.enqueueAction(action);
	},
	calculateTotal: function (component) {
		component.set("v.showFreeShippingMsg", true);
		var totalQuantity = 0;
		var subTotal = 0.0;
		var actualProductAmt = 0.0;
		var discountTotalAmt = 0.0;
        var discountWholeOrder = 0.0;
        var productIdList = [];
		var orderItemList = component.get("v.quotationItemList");
		if (orderItemList && orderItemList.length > 0) {
			for (var i = 0; i < orderItemList.length; i++) {
                var currentItem = orderItemList[i];
                if (currentItem.List_Price__c != undefined && currentItem.List_Price__c != '') {
                    subTotal += Number(currentItem.Quantity__c) * parseFloat(currentItem.List_Price__c);
                }

				//subTotal += Number(currentItem.Quantity__c) * parseFloat(currentItem.List_Price__c) + Number(currentItem.Discount_Amount__c) + Number(currentItem.Promo_Discount_Amount__c) + Number(currentItem.Whole_Order_Promo_Discount_Amount__c);
				//actualProductAmt += Number(currentItem.Quantity__c) * parseFloat(currentItem.List_Price__c) + Number(currentItem.Discount_Amount__c) + Number(currentItem.Promo_Discount_Amount__c) + Number(currentItem.Whole_Order_Promo_Discount_Amount__c);
                //discountTotalAmt += Number(currentItem.Discount_Amount__c) + Number(currentItem.Promo_Discount_Amount__c) + Number(currentItem.Whole_Order_Promo_Discount_Amount__c);

				actualProductAmt += Number(currentItem.Sub_Total__c);
                if (currentItem.List_Price__c != undefined && currentItem.List_Price__c != '') {
                    discountTotalAmt += Number(currentItem.Sub_Total__c) - Number(currentItem.Quantity__c) * parseFloat(currentItem.List_Price__c);
                }
				totalQuantity += Number(currentItem.Quantity__c);
                discountWholeOrder += Number(currentItem.Whole_Order_Promo_Discount_Amount__c);
                productIdList.push(currentItem.Product__c);
			}
		}
        var action = component.get("c.getPromotionProductLanuchDate");
        action.setParam("customerId", component.get("v.customerId"));
        action.setParam("productIdList", productIdList);
		var freightTermRuleFee = component.get("v.freightTermRuleFee");
		freightTermRuleFee = isNaN(freightTermRuleFee) ? 0 : freightTermRuleFee;
		component.set("v.quotation.Freight_Target_Fee__c", freightTermRuleFee);
		//var waivedShippingFee = Number(freightTermRuleFee) - Number(subTotal);
		var waivedShippingFee = Number(freightTermRuleFee) - Number(actualProductAmt);
		var waivedMsg = null;
		if (freightTermRuleFee == 0) {
			component.set("v.quotation.Freight_Fee_To_Be_Waived__c", "0.00");
			component.set("v.showFreeShippingMsg", false);
		} else {
			if (waivedShippingFee > 0) {
				component.set("v.quotation.Freight_Fee_To_Be_Waived__c", waivedShippingFee.toFixed(2));
				component.set("v.showFreeShippingMsg", true);
				component.set("v.isWaivedFreight", false);
			} else {
				component.set("v.quotation.Freight_Fee_To_Be_Waived__c", "0.00");
				component.set("v.showFreeShippingMsg", true);
				component.set("v.isWaivedFreight", true);
			}
		}

		component.set("v.quotation.Total_Quantity__c", totalQuantity);
		component.set("v.quotation.Product_Price__c", subTotal.toFixed(2));
		component.set("v.quotation.Actual_Total_Product_Amount__c", actualProductAmt.toFixed(2));
		component.set("v.quotation.Discount_Amount__c", discountTotalAmt.toFixed(2));
		component.set("v.quotation.Total_Saving", discountTotalAmt.toFixed(2));
        component.set("v.quotation.Whole_Order_Promo_Total_Saving", discountWholeOrder.toFixed(2));
        action.setCallback(this, function (response) {
			var state = response.getState();
			if (state === "SUCCESS") {
				var result = response.getReturnValue();
				if (result) {
                    for (var i = 0; i < orderItemList.length; i++) {
                        if (orderItemList[i].Lanch_Date__c === undefined || orderItemList[i].Lanch_Date__c === '') {
                            for (var j = 0; j < result.length; j++){
                                if (result[j].ProductCode == orderItemList[i].ProductCode__c) {
                                    orderItemList[i].Lanch_Date__c = result[j].Lanch_Date__c;
                                    if (orderItemList[i].Ship_Date__c < orderItemList[i].Lanch_Date__c) {
                                        orderItemList[i].shipDateGreaterLanuchDate = true;
                                        orderItemList[i].Ship_Date__c = orderItemList[i].Lanch_Date__c;
                                        orderItemList[i].minSelectDate = orderItemList[i].Lanch_Date__c;
                                    }
                                }
                            }
                        }
                    }
				}
			} else {
				console.log(response.getError());
			}
		});
		$A.enqueueAction(action);
	},
	getPaymentFreightRule: function (component, event, helper) {
		console.log("brandScorp select--->" + component.get("v.brandScope"));
		var action = component.get("c.getDropShipPaymentFreightTerm");
		action.setParam("customerId", component.get("v.customerId"));
		action.setParam("brandName", component.get("v.brandScope"));
		action.setParam("isDropShip", component.get("v.orderTypeVal"));
		action.setCallback(this, function (response) {
			var state = response.getState();
			if (state === "SUCCESS") {
				var result = JSON.parse(response.getReturnValue());
				if (result) {
					component.set("v.freightTermLabel", result.freightTermLabel);
					// component.set('v.paymentTermLabel', result.paymentTermLabel);
					// component.set('v.defaultPaymentTerm', result.paymentTerm);
					component.set("v.freightTermRuleFee", result.freightTermRuleFee);

					var purchaseOrder = component.get("v.quotation");
					var paymentTermCode = purchaseOrder.Payment_Term_Promotion_Code__c;
					if (!paymentTermCode) {
						component.set("v.paymentTermLabel", result.paymentTermLabel);
						component.set("v.paymentTermValue", result.paymentTerm);
						component.set("v.defaultPaymentTerm", result.paymentTerm);
						purchaseOrder.Payment_Term__c = result.paymentTerm;
					}

					// purchaseOrder.Payment_Term__c = result.paymentTerm;
					purchaseOrder.Freight_Term__c = result.freightTerm;
					console.log("-----test", result.paymentTerm);
					component.set("v.quotation", purchaseOrder);
				}
			} else {
				console.log(response.getError());
			}
		});
		$A.enqueueAction(action);
	},
	getUrlParameter: function (sParam) {
		var sPageURL = decodeURIComponent(window.location.search.substring(1)),
			sURLVariables = sPageURL.split("&"),
			sParameterName,
			i;

		for (i = 0; i < sURLVariables.length; i++) {
			sParameterName = sURLVariables[i].split("=");
			if (sParameterName[0] === sParam || sParameterName[0] === "0." + sParam) {
				return sParameterName[1] === undefined ? true : sParameterName[1];
			}
		}
	},
	currencyFormat: function (number) {
		var locale = $A.get("$Locale.lang");
		if (typeof number !== "number") {
			number = Number(number);
		}
		return number.toLocaleString(locale, { minimumFractionDigits: 2 });
	},
	rebindPaymentHandler: function (component, event, helper, isInit) {
		let strPreviousPaymentTermValue = component.get("v.paymentTermValue");
		let isCash = false;

		component.set("v.isBusy", true);
		component.set("v.needResearch", false);
		var brand = component.get("v.brandScope");
		var customerType = component.get("v.customerType");
		var customerCluster = component.get("v.customerCluster");
        var defaultPaymentTerm = component.get("v.defaultPaymentTerm");
        var defaultpaymentTermLabel = defaultPaymentTerm + ' ' +component.get("v.paymentTermLabel");
		if (defaultPaymentTerm == "NA032" || defaultPaymentTerm == "CA019") {
			isCash = true;
		}
		var customerOrgCode = component.get("v.customerOrgCode");
		//Update by Abby on 6/29/2020 for the payment term table will only be applied to customers whose cluster is OPE direct dealer and brand is EGO
		let NAScope = brand == "EGO" && (customerCluster == "CNA-CG11" || customerCluster == "CNA-CG10" || customerCluster == "CNA-CG20") && defaultPaymentTerm == "NA001" && customerOrgCode != "CCA";
		let CAScope = brand == "EGO" &&  customerOrgCode == "CCA" && (customerCluster == "CA-CG11" || customerCluster == "CA-CG12" || customerCluster == "CA-CG05");
		if ((NAScope || CAScope) && !isCash) {
			var paymentTermOptions = component.get("v.paymentTermAllOpts");
			var totalAmt = component.get("v.quotation.Product_Price__c");
            var options = [];var paymentValueList = [];
            var isOver = false;
			paymentTermOptions.forEach(function (element) {
				if (element.criteriaTo != null) {
					// if (element.criteriaFrom <= totalAmt && element.criteriaTo > totalAmt) {
					// 	options.push({ value: element.value, label: element.label });
					// }
				} else {
					if (element.criteriaFrom <= totalAmt) {
                        options.push({ value: element.value, label: element.label });
                        isOver = true;
                        paymentValueList.push(element.value);
					}
				}
            });
            if (isOver && paymentValueList.length > 0 && paymentValueList.indexOf(defaultPaymentTerm) < 0) {
                 options.push({ value: defaultPaymentTerm, label: defaultpaymentTermLabel });
            }

			if (options != null && options != undefined) {
				if (options.length > 0) {
					component.set("v.needResearch", true);
					component.set("v.multipleOptions", true);
				}
				// else if(options.length == 1){
				//     component.set('v.needResearch', false);
				//     component.set('v.multipleOptions', false);
				// }
			}

			let oldOptions = JSON.parse(JSON.stringify(component.get("v.paymentTermSelectOpt")));
			let hasOptionsChanged = this.checkPaymentTermOptionsChange(oldOptions, options);
			if (hasOptionsChanged) {
				if (!isInit) {
					component.set("v.paymentTermValue", null);
				}
				if (options != null && options.length > 0 && options != undefined) {
					// component.set('v.needResearch', true);
					component.set("v.paymentTermSelectOpt", options);
					if (!component.get("v.termsPromo")) {
						let objPaymentTerm = options.find((objItem) => objItem !== undefined && objItem.value === strPreviousPaymentTermValue);
						component.set("v.paymentTermValue", objPaymentTerm === undefined ? "" : objPaymentTerm.value);
						if (options.length === 1) {
							component.set("v.paymentTermLabel", options[0].label);
							component.set("v.paymentTermValue", options[0].value);
                        }
						if ($A.util.isEmpty(component.get("v.paymentTermValue")) || options.length > 1) {
							helper.showToast("Warning", $A.get("$Label.c.CCM_Payment_Term_Selection_Reminder"));
						}
					} else {
						component.set("v.needResearch", false);
					}
				}
			}
		} else {
			if (!component.get("v.termsPromo")) {
				component.set("v.paymentTermValue", component.get("v.defaultPaymentTerm"));
			}
			component.set("v.needResearch", false);
		}
		component.set("v.isBusy", false);
	},
	checkPaymentTermOptionsChange: function (oldOptions, newOptions) {
		let optionsChanged = false;
		if (oldOptions && newOptions && oldOptions.length == newOptions.length) {
			let oldvalues = [];
			oldOptions.forEach((element) => {
				oldvalues.push(element.value);
			});

			newOptions.forEach((element) => {
				if (!oldvalues.includes(element.value)) {
					optionsChanged = true;
				}
			});
		} else {
			optionsChanged = true;
		}
		return optionsChanged;
	},
	addOfferingItems: function (quotationItemList, index, promotionItem, indexObj, component) {
		var quotationItem = quotationItemList[index];
		var promotionCode = quotationItem.PromotionName__c;
		var ruleList = promotionItem.ruleList;
		var ruleName = ruleList[0].ruleName;
		var offeringList = ruleList[0].offeringList;
		var startIndex = indexObj.startindex;
		this.counterrorCheck(quotationItemList);
		if (promotionItem.promotion.Promotion_Type__c == "Price Break") {
			var miniRule = this.getMiniRuleInPriceBreak(promotionItem);
			offeringList = miniRule.offeringList;
			ruleName = miniRule.ruleName;
			var availableRule = this.getAvailableRuleInPriceBreak(quotationItemList, index, promotionItem);
			if (availableRule) {
				offeringList = availableRule.offeringList;
				ruleName = availableRule.ruleName;
			}
		}
		var times = this.getThresholdTimes(quotationItemList, index, promotionItem);
		if (times < 1) {
			if (this.hasMixMatch(promotionItem)) {
				times = 0;
			} else {
				times = 1;
			}
		}
		offeringList.forEach(function (oItem) {
			if (oItem.offering.RecordType.DeveloperName == "Specific_Free_Good") {
				var products = oItem.products;
				var priceBookEntrys = oItem.priceBookEntrys;
				if (products) {
					var pIndex = 0;
					products.forEach(function (element) {
						var offeringProduct = products[pIndex];
						var offeringPriceBookEntry = priceBookEntrys[pIndex];
						var unitPrice = 0.0;
						if (offeringPriceBookEntry.UnitPrice) {
							unitPrice = offeringPriceBookEntry.UnitPrice.toFixed(2);
						}
						var qty = 1;
						if (offeringProduct.Gift_Quantity__c) {
							qty = times * offeringProduct.Gift_Quantity__c;
						}
						var offeringIndex = parseInt(index) + 1 + startIndex;
						var offeringItem = {
							"Brand__c": offeringProduct.Product__r.Brand_Name__c,
							// "CS_Exchange_Rate__c" : offeringProduct.Product__r.CS_Exchange_Rate__c,
							"Product__c": offeringProduct.Product__c,
							"Product__r": {
								"SF_Description__c": offeringProduct.Product__r.SF_Description__c
							},
							"ProductCode__c": offeringProduct.Product__r.ProductCode,
							"Ship_Date__c": quotationItem.Ship_Date__c,
							"Quantity__c": qty,
							// "Quantity__c" : 1,
							"Price_Book__c": offeringPriceBookEntry.Pricebook2Id,
							"List_Price__c": unitPrice,
							"Unit_Price__c": 0.0,
							"Discount_Amount__c": 0.0,
							"Promo_Discount_Amount__c": -unitPrice * qty,
							"Whole_Order_Promo_Discount_Amount__c": 0.0,
							"Sub_Total__c": 0.0,
							"PromotionName__c": promotionItem.promotion.Promotion_Code_For_External__c,
							"Promotion__c": promotionItem.promotion.Id,
							"Regular_Promotion_Window__c": promotionItem.windowId,
							"Promotion_Rule_Name__c": ruleName,
							"isOffering": true,
							"HasPromo": false,
							"HasDiscount": true
						};
						var orgcode = component.get("v.OrgCode");
						if (orgcode != "CCA") {
							offeringItem.CS_Exchange_Rate__c = offeringProduct.Product__r.CS_Exchange_Rate__c ? offeringProduct.Product__r.CS_Exchange_Rate__c : 1;
						} else {
							offeringItem.CS_Exchange_Rate__c = 1;
						}
						quotationItemList.splice(offeringIndex, 0, offeringItem);
						startIndex++;
						pIndex++;
					});
				}
			} else if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
				var products = oItem.products;
				var priceBookEntrys = oItem.priceBookEntrys;
				if (products) {
					var pIndex = 0;
					products.forEach(function (pItem) {
						if (pItem.selected) {
							var offeringProduct = products[pIndex];
							var offeringPriceBookEntry = priceBookEntrys[pIndex];
							var unitPrice = 0.0;
							if (offeringPriceBookEntry.UnitPrice) {
								unitPrice = offeringPriceBookEntry.UnitPrice.toFixed(2);
							}
							var qty = 1;
							if (offeringProduct.Number__c) {
								qty = times * offeringProduct.Number__c;
							}
							var offeringIndex = parseInt(index) + 1 + startIndex;
							var offeringItem = {
								"Brand__c": offeringProduct.Product__r.Brand_Name__c,
								"Product__c": offeringProduct.Product__c,
								"Product__r": {
									"SF_Description__c": offeringProduct.Product__r.SF_Description__c
								},
								"ProductCode__c": offeringProduct.Product__r.ProductCode,
								"Ship_Date__c": quotationItem.Ship_Date__c,
								"Quantity__c": 1,
								"Price_Book__c": offeringPriceBookEntry.Pricebook2Id,
								"List_Price__c": unitPrice,
								"Unit_Price__c": 0.0,
								"Discount_Amount__c": 0.0,
								"Promo_Discount_Amount__c": -unitPrice * 1,
								"Whole_Order_Promo_Discount_Amount__c": 0.0,
								"Sub_Total__c": 0.0,
								"PromotionName__c": promotionItem.promotion.Promotion_Code_For_External__c,
								"Promotion__c": promotionItem.promotion.Id,
								"Regular_Promotion_Window__c": promotionItem.windowId,
								"Promotion_Rule_Name__c": ruleName,
								"isOffering": true,
								"HasPromo": false,
								"HasDiscount": true,
								"isPool": true,
								"Promotion": promotionItem,
								"isExceed": false,
								"isLess": qty > 1 ? true : false
							};
							var orgcode = component.get("v.OrgCode");
							if (orgcode != "CCA") {
								offeringItem.CS_Exchange_Rate__c = offeringProduct.Product__r.CS_Exchange_Rate__c ? offeringProduct.Product__r.CS_Exchange_Rate__c : 1;
							} else {
								offeringItem.CS_Exchange_Rate__c = 1;
							}
							quotationItemList.splice(offeringIndex, 0, offeringItem);
							startIndex++;
						}
						pIndex++;
					});
				}
			}
		});
	},
	updateThresholdItemMixMatch: function (quotationItemList, index, promotionItem) {
		var helper = this;
		var quotationItem = quotationItemList[index];
		var ruleList = promotionItem.ruleList;
		var thresholdList = ruleList[0].thresholdList;
		var offeringList = ruleList[0].offeringList;
		var hasFreeGoods = this.hasFreeGoods(ruleList[0]);
		var promotionCode = promotionItem.promotion.Promotion_Code_For_External__c;
		var ruleName = quotationItemList[index].Promotion_Rule_Name__c;
		this.counterrorCheck(quotationItemList);
		var times = this.getThresholdTimes(quotationItemList, index, promotionItem);
		if (times < 1) {
			times = 1;
		}
		var freeGoods = []; //装赠品
		var buyGoods = []; //买品
		if (hasFreeGoods) {
			quotationItemList.forEach(function (qItem) {
				qItem.additionalDiscount = 0;
				qItem.additionalAmountOff = 0;
				if (qItem.Promotion_Rule_Name__c == ruleName && qItem.isOffering) {
					//赠品
					var productCode = qItem.ProductCode__c;
					thresholdList.forEach(function (tItem) {
						var products = tItem.products;
						var thresholdType = tItem.threshold.RecordType.DeveloperName;
						products.forEach(function (pItem) {
							if (pItem.Product__r.ProductCode == productCode) {
								if (thresholdType == "By_Mix_Match") {
									qItem.isMeet = true;
									// qItem.additionalDiscount = pItem.Additional_Discount__c; // modify by austin,计算Unit Price
									// console.log('qItem.additionalDiscount'+qItem.additionalDiscount);
								}
							}
						});
					});
					freeGoods.push(qItem);
				} else if (qItem.Promotion_Rule_Name__c == ruleName && (qItem.HasPromo || qItem.isThreshold)) {
					if (qItem.isMix) {
						qItem.isMeet = true;
					}
					var productCode = qItem.ProductCode__c;
					thresholdList.forEach(function (tItem) {
						var products = tItem.products;
						var thresholdType = tItem.threshold.RecordType.DeveloperName;
						products.forEach(function (pItem) {
							if (pItem.Product__r.ProductCode == productCode) {
								// 24.12.30 修复有赠品的Mix&Match未计算行上的additionalDiscount和additionalAmountOff
								if (thresholdType == "By_Mix_Match") {
									qItem.isMeet = true;
									qItem.additionalDiscount = !pItem.Additional_Discount__c ? 0 : pItem.Additional_Discount__c;
                                    qItem.additionalAmountOff = (!pItem.Additional_Amount_Off__c) ? 0 : pItem.Additional_Amount_Off__c;
								}
								// 24.12.30 end
								if (thresholdType == "By_Full_Pallet_Quantity_of_Specific_Product") {
									//var minimumQty = pItem.Minimum_Quantity__c;
									var minimumQty = pItem.Product__r.Full_Pallet_Quantity__c;
									qItem.Quantity__c = Math.floor(qItem.Quantity__c / minimumQty) * minimumQty;
									qItem.Sub_Total__c = (qItem.List_Price__c * qItem.Quantity__c).toFixed(2);
								}
							}
						});
					});
					buyGoods.push(qItem);
				}
			});
		} else {
			quotationItemList.forEach(function (qItem) {
				qItem.additionalDiscount = 0;
				qItem.additionalAmountOff = 0;
				if (qItem.Promotion_Rule_Name__c == ruleName) {
					var productCode = qItem.ProductCode__c;
					thresholdList.forEach(function (tItem) {
						var products = tItem.products;
						var thresholdType = tItem.threshold.RecordType.DeveloperName;
						products.forEach(function (pItem) {
							if (pItem.Product__r.ProductCode == productCode) {
								if (thresholdType == "By_Mix_Match") {
									qItem.isMeet = true;
									qItem.additionalDiscount = !pItem.Additional_Discount__c ? 0 : pItem.Additional_Discount__c; // modify by austin,计算Unit Price
									// 24.10.24: 新增Amount Off计算
                                    qItem.additionalAmountOff = (!pItem.Additional_Amount_Off__c) ? 0 : pItem.Additional_Amount_Off__c;
                                    // 24.10.24 end
								}
								if (thresholdType == "By_Full_Pallet_Quantity_of_Specific_Product") {
									//var minimumQty = pItem.Minimum_Quantity__c;
									var minimumQty = pItem.Product__r.Full_Pallet_Quantity__c;
									qItem.Quantity__c = Math.floor(qItem.Quantity__c / minimumQty) * minimumQty;
									qItem.Sub_Total__c = (qItem.List_Price__c * qItem.Quantity__c).toFixed(2);
								}
							}
						});
					});
					buyGoods.push(qItem);
				}
			});
		}
		let _quotationItemList = [];

		let hasCalculatedDisacount = false;
		offeringList.forEach(function (oItem) {
			var offeringType = oItem.offering.RecordType.DeveloperName;
			if (offeringType == "Discount_Off") {
				hasCalculatedDisacount = true;
				var discountOff = oItem.offering.Discount_Off__c;
				buyGoods.forEach(function (qItem) {
					if (qItem.Promotion_Rule_Name__c == ruleName && !qItem.isOffering) {
						// 24.10.17: 折扣金额 = -1 * ({(ListPrice - Threshold产品折扣金额) * 数量 * [1 - (1 - Threshold产品折扣率) * (1 - Offering折扣率)]} + Threshold产品折扣金额 * 数量)
                        qItem.Promo_Discount_Amount__c = -1 * Number((((qItem.List_Price__c - qItem.additionalAmountOff) * qItem.Quantity__c) * (1 - (1 - qItem.additionalDiscount / 100) * (1 - discountOff / 100))) + qItem.additionalAmountOff * qItem.Quantity__c);
                        // 24.10.17 end
						if (qItem.Quantity__c && qItem.Quantity__c > 0) {
							qItem.Sub_Total__c = (
								Number(qItem.List_Price__c) * Number(qItem.Quantity__c) +
								Number(qItem.Promo_Discount_Amount__c) +
								Number(qItem.Discount_Amount__c) +
								Number(qItem.Whole_Order_Promo_Discount_Amount__c)
							).toFixed(2);
							qItem.Sub_Total__c = Number(qItem.Sub_Total__c).toFixed(2);
							//qItem.Unit_Price__c = Number((qItem.Sub_Total__c/qItem.Quantity__c).toFixed(2));
							//qItem.Unit_Price__c = Number((qItem.List_Price__c * (1 - discountOff/100)).toFixed(2));
							// qItem.Unit_Price__c = helper.calculateNumMulti(helper.calculateNumMulti(qItem.List_Price__c * (1 - qItem.additionalDiscount / 100), 100 - discountOff), 0.01);
							// 24.10.24: 单价 = (ListPrice - Threshold产品折扣金额) * (1 - Threshold产品折扣率) * (1 - Offering折扣率)
                            qItem.Unit_Price__c = Number((qItem.List_Price__c - qItem.additionalAmountOff) * (1 - qItem.additionalDiscount / 100) * (1 - discountOff / 100)).toFixed(2);
                            // 24.10.24 end
							// qItem.Unit_Price__c = helper.calculateNumMulti(Math.round(helper.calculateNumMulti(qItem.Unit_Price__c,100)), 0.01);
						} else {
							qItem.Sub_Total__c = 0.0;
							// 24.10.24: 单价 = (ListPrice - Threshold产品折扣金额) * (1 - Threshold产品折扣率)
                            qItem.Unit_Price__c = Number((qItem.List_Price__c - qItem.additionalAmountOff) * (1 - qItem.additionalDiscount / 100)).toFixed(2);
                            // 24.10.24 end
						}
						qItem.Promo_Discount_Amount__c = Number(Number(qItem.Promo_Discount_Amount__c).toFixed(2));
					}
					_quotationItemList.push(qItem);
				});
				if (freeGoods.length > 0) {
					freeGoods.forEach(function (qItem) {
						if (qItem.Promotion_Rule_Name__c == ruleName && !qItem.isOffering) {
							qItem.Promo_Discount_Amount__c = Number(-(qItem.List_Price__c * qItem.Quantity__c));
							qItem.Sub_Total__c = 0;
							qItem.Unit_Price__c = 0;
							qItem.Promo_Discount_Amount__c = Number(Number(qItem.Promo_Discount_Amount__c).toFixed(2));
						}
						_quotationItemList.push(qItem);
					});
				}
			} else if (offeringType == "Amount_Off") {
				hasCalculatedDisacount = true;
				var amountOff = oItem.offering.Amount_Off__c;
				buyGoods.forEach(function (qItem) {
					if (qItem.Promotion_Rule_Name__c == ruleName && !qItem.isOffering) {
						// 24.10.24: 折扣金额 = (ListPrice * Threshold产品折扣率 + Threshold产品折扣金额 + Offering折扣金额) * 数量
                        // 单个折扣金额
                        let _unitDiscountAmount = Number(qItem.List_Price__c) * (qItem.additionalDiscount / 100) + qItem.additionalAmountOff + amountOff;
                        if(_unitDiscountAmount > qItem.List_Price__c){
                            qItem.Promo_Discount_Amount__c = -1 * Number(qItem.List_Price__c) * Number(qItem.Quantity__c);
                        }else{
                            qItem.Promo_Discount_Amount__c = -1 * _unitDiscountAmount * Number(qItem.Quantity__c);
                        }
                        // 24.10.24 end
						// qItem.Promo_Discount_Amount__c = amountOff > qItem.List_Price__c ? (Number(qItem.List_Price__c) * Number(qItem.Quantity__c) * (-1 - qItem.additionalDiscount / 100)) : Number(-(amountOff * Number(qItem.Quantity__c) + Number(qItem.List_Price__c) * Number(qItem.Quantity__c) * (qItem.additionalDiscount / 100)));
						//qItem.Promo_Discount_Amount__c = Number(qItem.Promo_Discount_Amount__c.toFixed(2));
						if (qItem.Quantity__c && qItem.Quantity__c > 0) {
							qItem.Sub_Total__c = (
								Number(qItem.List_Price__c) * Number(qItem.Quantity__c) +
								Number(qItem.Promo_Discount_Amount__c) +
								Number(qItem.Discount_Amount__c) +
								Number(qItem.Whole_Order_Promo_Discount_Amount__c)
							).toFixed(2);
							qItem.Sub_Total__c = Number(qItem.Sub_Total__c).toFixed(2);
							// 24.10.24: 单价 = (ListPrice - 单个折扣金额)
                            qItem.Unit_Price__c = _unitDiscountAmount > qItem.List_Price__c ? 0.00 : (Number((qItem.List_Price__c - _unitDiscountAmount).toFixed(2)));
                            // 24.10.24 end
						} else {
							qItem.Sub_Total__c = 0.0;
							// 24.10.24: 单价 = (ListPrice - 不含Offering的单个折扣金额)
                            qItem.Unit_Price__c = Number(qItem.List_Price__c - ((qItem.List_Price__c) * (qItem.additionalDiscount / 100) + qItem.additionalAmountOff)).toFixed(2);
                            // 24.10.24 end
						}
						qItem.Promo_Discount_Amount__c = Number(Number(qItem.Promo_Discount_Amount__c).toFixed(2));
					}
					_quotationItemList.push(qItem);
				});
				if (freeGoods.length > 0) {
					freeGoods.forEach(function (qItem) {
						if (qItem.Promotion_Rule_Name__c == ruleName && !qItem.isOffering) {
							qItem.Promo_Discount_Amount__c = Number(-(qItem.List_Price__c * qItem.Quantity__c));
							qItem.Sub_Total__c = 0;
							qItem.Unit_Price__c = 0;
							qItem.Promo_Discount_Amount__c = Number(Number(qItem.Promo_Discount_Amount__c).toFixed(2));
						}
						_quotationItemList.push(qItem);
					});
				}
			} else {
				if (hasCalculatedDisacount) {
					return;
				}
				buyGoods.forEach(function (qItem) {
					// 24.10.24: 单个折扣金额
                    let _unitDiscountAmount = Number(qItem.List_Price__c) * (qItem.additionalDiscount / 100) + qItem.additionalAmountOff;
                    qItem.Promo_Discount_Amount__c = -1 * Number(qItem.Quantity__c * _unitDiscountAmount);
                    // 24.10.24 end
					if (qItem.Quantity__c && qItem.Quantity__c > 0) {
						qItem.Sub_Total__c = (
							Number(qItem.List_Price__c) * Number(qItem.Quantity__c) +
							Number(qItem.Promo_Discount_Amount__c) +
							Number(qItem.Discount_Amount__c) +
							Number(qItem.Whole_Order_Promo_Discount_Amount__c)
						).toFixed(2);
						qItem.Sub_Total__c = Number(qItem.Sub_Total__c).toFixed(2);
						qItem.Unit_Price__c = Number(qItem.List_Price__c - _unitDiscountAmount).toFixed(2);
					} else {
						qItem.Sub_Total__c = 0.0;
						qItem.Unit_Price__c = Number(qItem.List_Price__c - _unitDiscountAmount).toFixed(2);
					}
					qItem.Promo_Discount_Amount__c = Number(Number(qItem.Promo_Discount_Amount__c).toFixed(2));
				});
				freeGoods.forEach(function (qItem) {
					if (qItem.Promotion_Rule_Name__c == ruleName && !qItem.isOffering) {
						qItem.Promo_Discount_Amount__c = Number(-(qItem.List_Price__c * qItem.Quantity__c));
						qItem.Sub_Total__c = 0;
						qItem.Unit_Price__c = 0;
						qItem.Promo_Discount_Amount__c = Number(Number(qItem.Promo_Discount_Amount__c).toFixed(2));
					}
					_quotationItemList.push(qItem);
				});
			}
		});
		quotationItemList = _quotationItemList;
	},
	updateThresholdItem: function (quotationItemList, index, promotionItem) {
		var helper = this;
		var quotationItem = quotationItemList[index];
		var ruleList = promotionItem.ruleList;
		var thresholdList = ruleList[0].thresholdList;
		var offeringList = ruleList[0].offeringList;
		var hasFreeGoods = this.hasFreeGoods(ruleList[0]);
		var promotionCode = promotionItem.promotion.Promotion_Code_For_External__c;
		var ruleName = quotationItemList[index].Promotion_Rule_Name__c;
		this.updateMultipleQty(quotationItemList, index, promotionItem);
		this.counterrorCheck(quotationItemList);
		if (promotionItem.promotion.Promotion_Type__c == "Price Break") {
			var miniRule = this.getMiniRuleInPriceBreak(promotionItem);
			thresholdList = miniRule.thresholdList;
			offeringList = miniRule.offeringList;
			var availableRule = this.getAvailableRuleInPriceBreak(quotationItemList, index, promotionItem);
			if (availableRule) {
				thresholdList = availableRule.thresholdList;
				offeringList = availableRule.offeringList;
			}
		}
		var times = this.getThresholdTimes(quotationItemList, index, promotionItem);
		if (times < 1) {
			times = 1;
		}
		if (promotionItem.promotion.Promotion_Type__c == "Price Break" || hasFreeGoods) {
			quotationItemList.forEach(function (qItem) {
				if (qItem.Promotion_Rule_Name__c == ruleName && qItem.isThreshold) {
					var productCode = qItem.ProductCode__c;
					thresholdList.forEach(function (tItem) {
						var products = tItem.products;
						var thresholdType = tItem.threshold.RecordType.DeveloperName;
						products.forEach(function (pItem) {
							if (pItem.Product__r.ProductCode == productCode) {
								if (thresholdType == "By_Quantity_of_Specific_Product") {
									var minimumQty = pItem.Increment_For_Free_Goods__c ? pItem.Increment_For_Free_Goods__c : pItem.Minimum_Quantity__c;
									qItem.Quantity__c = minimumQty * times;
									qItem.Sub_Total__c = (qItem.List_Price__c * qItem.Quantity__c).toFixed(2);
								} else if (thresholdType == "By_Amount_of_Specific_Product") {
									var minimumAmt = pItem.Minimum_Amount__c;
									var minimumQty = Math.floor(minimumAmt / qItem.List_Price__c);
									if (minimumQty * qItem.List_Price__c < minimumAmt) {
										minimumQty += 1;
									}
									qItem.Quantity__c = minimumQty * times;
									qItem.Sub_Total__c = (qItem.List_Price__c * qItem.Quantity__c).toFixed(2);
								} else if (thresholdType == "By_Full_Pallet_Quantity_of_Specific_Product") {
									var minimumQty = pItem.Minimum_Quantity__c * pItem.Product__r.Full_Pallet_Quantity__c;
									qItem.Quantity__c = minimumQty * times;
									qItem.Sub_Total__c = (qItem.List_Price__c * qItem.Quantity__c).toFixed(2);
								} else if (thresholdType == "By_Mix_Match") {
									qItem.isMeet = true;
								}
								//Ynako 对于BOGO类型的Promotion，一个Rule存在多条Threshold，当通过修改第一个产品的数量，使第二个产品的数量达到case qty倍数时，没有清除不满足case qty倍数的错误提示。
								// change0207 Start
								// qItem.Quantity__c = 1143;

								if (pItem.Multiple_Control__c == "Inner Box Multiple") {
									var promquantity = qItem.Quantity__c;
									for (var j = 0; j < quotationItemList.length; j++) {
										if (j != index && quotationItemList[j].ProductCode__c == qItem.ProductCode__c) {
											var intj = j;
											var promquantity = Number(promquantity) + Number(quotationItemList[j].Quantity__c);
										}
									}
									var validateqty = Number(promquantity) % Number(pItem.Product__r.CS_Exchange_Rate__c);
									if (validateqty != 0) {
										qItem.meetalert = 1;
										qItem.meetalertmodel = Number(intj) + 1;
									} else {
										qItem.meetalert = 0;
									}
								}

								// change0207 End
							}
						});
					});
				} else if (qItem.Promotion_Rule_Name__c == ruleName && qItem.HasPromo) {
					if (qItem.isMix) {
						qItem.isMeet = true;
					}

					var productCode = qItem.ProductCode__c;
					thresholdList.forEach(function (tItem) {
						var products = tItem.products;
						var thresholdType = tItem.threshold.RecordType.DeveloperName;
						products.forEach(function (pItem) {
							if (pItem.Product__r.ProductCode == productCode) {
								if (thresholdType == "By_Full_Pallet_Quantity_of_Specific_Product") {
									//var minimumQty = pItem.Minimum_Quantity__c;
									var minimumQty = pItem.Product__r.Full_Pallet_Quantity__c;
									qItem.Quantity__c = Math.floor(qItem.Quantity__c / minimumQty) * minimumQty;
									qItem.Sub_Total__c = (qItem.List_Price__c * qItem.Quantity__c).toFixed(2);
								}
								//Ynako 对于BOGO类型的Promotion，一个Rule存在多条Threshold，当通过修改第一个产品的数量，使第二个产品的数量达到case qty倍数时，没有清除不满足case qty倍数的错误提示。
								// change0207 Start
								// qItem.Quantity__c = 1143;

								if (pItem.Multiple_Control__c == "Inner Box Multiple") {
									var promquantity = qItem.Quantity__c;
									for (var j = 0; j < quotationItemList.length; j++) {
										if (j != index && quotationItemList[j].ProductCode__c == qItem.ProductCode__c) {
											var intj = j;
											var promquantity = Number(promquantity) + Number(quotationItemList[j].Quantity__c);
										}
									}
									var validateqty = Number(promquantity) % Number(pItem.Product__r.CS_Exchange_Rate__c);
									if (validateqty != 0) {
										qItem.meetalert = 1;
										qItem.meetalertmodel = Number(intj) + 1;
									} else {
										qItem.meetalert = 0;
									}
								}

								// change0207 End
							}
						});
					});
				}
			});
		} else {
			quotationItemList.forEach(function (qItem) {
				if (qItem.Promotion_Rule_Name__c == ruleName) {
					var productCode = qItem.ProductCode__c;
					thresholdList.forEach(function (tItem) {
						var products = tItem.products;
						var thresholdType = tItem.threshold.RecordType.DeveloperName;
						products.forEach(function (pItem) {
							if (pItem.Product__r.ProductCode == productCode) {
								if (thresholdType == "By_Mix_Match") {
									qItem.isMeet = true;
								}
								if (thresholdType == "By_Full_Pallet_Quantity_of_Specific_Product") {
									//var minimumQty = pItem.Minimum_Quantity__c;
									var minimumQty = pItem.Product__r.Full_Pallet_Quantity__c;
									qItem.Quantity__c = Math.floor(qItem.Quantity__c / minimumQty) * minimumQty;
									qItem.Sub_Total__c = (qItem.List_Price__c * qItem.Quantity__c).toFixed(2);
								}
								//Ynako 对于BOGO类型的Promotion，一个Rule存在多条Threshold，当通过修改第一个产品的数量，使第二个产品的数量达到case qty倍数时，没有清除不满足case qty倍数的错误提示。
								// change0207 Start
								// qItem.Quantity__c = 1143;

								if (pItem.Multiple_Control__c == "Inner Box Multiple") {
									var promquantity = qItem.Quantity__c;
									for (var j = 0; j < quotationItemList.length; j++) {
										if (j != index && quotationItemList[j].ProductCode__c == qItem.ProductCode__c) {
											var intj = j;
											var promquantity = Number(promquantity) + Number(quotationItemList[j].Quantity__c);
										}
									}
									var validateqty = Number(promquantity) % Number(pItem.Product__r.CS_Exchange_Rate__c);
									if (validateqty != 0) {
										qItem.meetalert = 1;
										qItem.meetalertmodel = Number(intj) + 1;
									} else {
										qItem.meetalert = 0;
									}
								}

								// change0207 End
							}
						});
					});
				}
			});
		}

		offeringList.forEach(function (oItem) {
			var offeringType = oItem.offering.RecordType.DeveloperName;
			if (offeringType == "Discount_Off") {
				var discountOff = oItem.offering.Discount_Off__c;
				quotationItemList.forEach(function (qItem) {
					if (qItem.Promotion_Rule_Name__c == ruleName && !qItem.isOffering) {
						qItem.Promo_Discount_Amount__c = -(qItem.List_Price__c * qItem.Quantity__c) * (discountOff / 100);
						if (qItem.Quantity__c && qItem.Quantity__c > 0) {
							qItem.Sub_Total__c = (
								Number(qItem.List_Price__c) * Number(qItem.Quantity__c) +
								Number(qItem.Promo_Discount_Amount__c) +
								Number(qItem.Discount_Amount__c) +
								Number(qItem.Whole_Order_Promo_Discount_Amount__c)
							).toFixed(2);
							qItem.Sub_Total__c = Number(qItem.Sub_Total__c).toFixed(2);
							//qItem.Unit_Price__c = Number((qItem.Sub_Total__c/qItem.Quantity__c).toFixed(2));
							//qItem.Unit_Price__c = Number((qItem.List_Price__c * (1 - discountOff/100)).toFixed(2));
							qItem.Unit_Price__c = helper.calculateNumMulti(helper.calculateNumMulti(qItem.List_Price__c, 100 - discountOff), 0.01);
							qItem.Unit_Price__c = helper.calculateNumMulti(Math.round(helper.calculateNumMulti(qItem.Unit_Price__c, 100)), 0.01);
						} else {
							qItem.Sub_Total__c = 0.0;
							qItem.Unit_Price__c = qItem.List_Price__c;
						}
						qItem.Promo_Discount_Amount__c = Number(qItem.Promo_Discount_Amount__c.toFixed(2));
					}
				});
			} else if (offeringType == "Amount_Off") {
				var amountOff = oItem.offering.Amount_Off__c;
				// var totalAmountOff = amountOff*times;
				// var totalAmount = 0.00;
				// quotationItemList.forEach(function(qItem){
				//     if(qItem.Promotion_Rule_Name__c == ruleName && !qItem.isOffering){
				//         totalAmount+=Number(qItem.List_Price__c) * Number(qItem.Quantity__c) + Number(qItem.Discount_Amount__c);
				//     }
				// });
				quotationItemList.forEach(function (qItem) {
					if (qItem.Promotion_Rule_Name__c == ruleName && !qItem.isOffering) {
						qItem.Promo_Discount_Amount__c = amountOff > qItem.List_Price__c ? Number(qItem.List_Price__c) * Number(qItem.Quantity__c) * -1 : amountOff * Number(qItem.Quantity__c) * -1;
						//qItem.Promo_Discount_Amount__c = Number(qItem.Promo_Discount_Amount__c.toFixed(2));
						if (qItem.Quantity__c && qItem.Quantity__c > 0) {
							qItem.Sub_Total__c = (
								Number(qItem.List_Price__c) * Number(qItem.Quantity__c) +
								Number(qItem.Promo_Discount_Amount__c) +
								Number(qItem.Discount_Amount__c) +
								Number(qItem.Whole_Order_Promo_Discount_Amount__c)
							).toFixed(2);
							qItem.Sub_Total__c = Number(qItem.Sub_Total__c).toFixed(2);
							qItem.Unit_Price__c = amountOff > qItem.List_Price__c ? 0.0 : (Number(qItem.List_Price__c * 1000 - amountOff * 1000) / 1000).toFixed(2);
						} else {
							qItem.Sub_Total__c = 0.0;
							qItem.Unit_Price__c = qItem.List_Price__c;
						}
						qItem.Promo_Discount_Amount__c = Number(qItem.Promo_Discount_Amount__c.toFixed(2));
					}
				});
			}
		});
	},
	updateOfferingItems: function (quotationItemList, index, promotionItem) {
		var quotationItem = quotationItemList[index];
		var ruleList = promotionItem.ruleList;
		var offeringList = ruleList[0].offeringList;
		var ruleName = quotationItemList[index].Promotion_Rule_Name__c;
		this.counterrorCheck(quotationItemList);
		if (promotionItem.promotion.Promotion_Type__c == "Price Break") {
			var miniRule = this.getMiniRuleInPriceBreak(promotionItem);
			offeringList = miniRule.offeringList;
			var availableRule = this.getAvailableRuleInPriceBreak(quotationItemList, index, promotionItem);
			if (availableRule) {
				offeringList = availableRule.offeringList;
			}
		}
		var promotionCode = quotationItem.PromotionName__c;
		var times = this.getThresholdTimes(quotationItemList, index, promotionItem);
		if (times < 1) {
			times = 1;
		}
		offeringList.forEach(function (oItem) {
			if (oItem.offering.RecordType.DeveloperName == "Specific_Free_Good") {
				var products = oItem.products;
				products.forEach(function (pItem) {
					quotationItemList.forEach(function (qItem) {
						if (qItem.Promotion_Rule_Name__c == ruleName && qItem.isOffering && qItem.ProductCode__c == pItem.Product__r.ProductCode) {
							qItem.Quantity__c = times * pItem.Gift_Quantity__c;
							qItem.Promo_Discount_Amount__c = -qItem.List_Price__c * Number(qItem.Quantity__c);
						}
					});
				});
			} else if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
			}
		});
		// this.counterrorCheck(quotationItemList);
	},
	// 24.10.22: 联动按倍数更新使用了PD Promotion的买品数量
    updateMultipleQty: function (quotationItemList, index, promotionItem) {
        if(["Price Discount"].includes(promotionItem.promotion.Promotion_Type__c)){
            let quotationItem = quotationItemList[index];
            let quotationItemQty = quotationItem.Quantity__c;
            let quotationItemPromotionThreshold = promotionItem.ruleList[0].thresholdList.find(_threshold => {
                return _threshold.products.find(_product => {
                    return _product.Product__c === quotationItem.Product__c;
                });
            })
            console.log('updateMultipleQty quotationItemPromotionThreshold ->',quotationItemPromotionThreshold);
            let quotationItemPromotionProductBuyQty = quotationItemPromotionThreshold.threshold.Buy_Qty__c;
            let _multiple = quotationItemQty / quotationItemPromotionProductBuyQty;
            console.log('updateMultipleQty _multiple ->',_multiple);
            let otherQuotationItemsWithSamePromotionRule = quotationItemList.filter(_quotationItem => {
                return _quotationItem.Product__c !== quotationItem.Product__c
                    && _quotationItem.Promotion && _quotationItem.Promotion.promotion.Id === promotionItem.promotion.Id
                    && _quotationItem.Promotion.ruleList[0].ruleId === promotionItem.ruleList[0].ruleId
            });
            console.log('updateMultipleQty otherQuotationItemsWithSamePromotionRule ->',otherQuotationItemsWithSamePromotionRule);
            otherQuotationItemsWithSamePromotionRule.forEach(_quotationItem => {
                let currentQuotationItemPromotionThreshold = promotionItem.ruleList[0].thresholdList.find(_threshold => {
                    return _threshold.products.find(_product => {
                        return _product.Product__c === _quotationItem.Product__c;
                    });
                })
                let currentQuotationItemPromotionProductBuyQty = currentQuotationItemPromotionThreshold.threshold.Buy_Qty__c;
                _quotationItem.Quantity__c = _multiple * currentQuotationItemPromotionProductBuyQty;
            })
        }
    },
	counterrorCheck: function (quotationItemList) {
		var ExpirateDate = quotationItemList[0].ExpirateDate;
		for (var s = 0; s < quotationItemList.length; s++) {
			//在apply promo之后 ， 检查price break和price discount的quantity是否满足buy qty的倍数，如不满足，自动向上取值。
			var buyqty = 1;

			if (quotationItemList[s].Promotion) {
				console.log('counterrorCheck quotationItemList[s].Promotion -> ', quotationItemList[s].Promotion,  's =', s);
				if (quotationItemList[s].Promotion.ruleList) {
					for (var i = 0; i < quotationItemList[s].Promotion.ruleList.length; i++) {
						if (quotationItemList[s].Promotion.ruleList[i] && quotationItemList[s].Promotion.ruleList[i].thresholdList) {
							for (var j = 0; j < quotationItemList[s].Promotion.ruleList[i].thresholdList.length; j++) {
								//BUY QTY promotion如果是price Discount 或者 Price Break，查找BuyQty
								if (quotationItemList[s].Promotion.promotion.Promotion_Type__c == "Price Discount" || quotationItemList[s].Promotion.promotion.Promotion_Type__c == "Price Break") {
									if (
										quotationItemList[s].Promotion.ruleList[i].thresholdList[j]
										&& ['By_Quantity_of_Specific_Product', 'By_Amount_of_Specific_Product'].includes(quotationItemList[s].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName)
                                        && quotationItemList[s].Promotion.ruleList[i].thresholdList[j].products[0].Product__c === quotationItemList[s].Product__c
									) {
										var buyqty = quotationItemList[s].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
										// console.log('counterrorCheck quotationItemList[s].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c -> ', buyqty,  's =', s, 'i = ', i, 'j = ', j);
                                        // console.log('counterrorCheck before quotationItemList[s].Quantity__c -> ', quotationItemList[s].Quantity__c,  's =', s, 'i = ', i, 'j = ', j);
                                        // console.log('counterrorCheck quotationItemList[s].Quantity__c % Number(buyqty) -> ', quotationItemList[s].Quantity__c % Number(buyqty),  's =', s, 'i = ', i, 'j = ', j);
										if (buyqty && buyqty != 0 && buyqty != 1) {
											//for buyqty
											if (quotationItemList[s].Quantity__c % Number(buyqty) != 0) {
												for (var a = 0; a < buyqty; a++) {
													if ((Number(quotationItemList[s].Quantity__c) + a) % Number(buyqty) == 0) {
														quotationItemList[s].Quantity__c = Number(quotationItemList[s].Quantity__c) + a;
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
			console.log('counterrorCheck after quotationItemList[s].Quantity__c -> ', quotationItemList[s].Quantity__c, 's =', s);

			var woqty = quotationItemList[s].Quantity__c;
			for (j = 0; j < quotationItemList.length; j++) {
				if (quotationItemList[j].ProductCode__c == quotationItemList[s].ProductCode__c) {
					if (j != s) {
						var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
						var needcheck = 1;
					}
				}
			}
			if (needcheck == 1) {
				var validateqty = Number(woqty) % Number(quotationItemList[s].CS_Exchange_Rate__c);
			} else {
				var validateqty = Number(quotationItemList[s].Quantity__c) % Number(quotationItemList[s].CS_Exchange_Rate__c);
			}

			if (validateqty != 0 && ExpirateDate != false) {
				var needcheckqty = false;
				var notonlyfreegoods = false;
				for (var x = 0; x < quotationItemList.length; x++) {
					if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
						var notonlyfreegoods = true;
						if (quotationItemList[x].Promotion) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
														// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						} else {
							var needcheckqty = true;
						}
					}
				}

				if (notonlyfreegoods == false) {
					var needcheckfreegoodsqty = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
														// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckfreegoodsqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					if (needcheckfreegoodsqty == true) {
						quotationItemList[s].counterror = true;
					} else {
						quotationItemList[s].counterror = false;
					}
				} else {
					if (needcheckqty == true) {
						quotationItemList[s].counterror = true;
					} else {
						quotationItemList[s].counterror = false;
					}
				}
			} else {
				quotationItemList[s].counterror = false;
			}
		}
	},
	removeOfferingItemsOutOfPool: function (quotationItemList, index, promotionItem) {
		var quotationItem = quotationItemList[index];
		var ruleList = promotionItem.ruleList;
		var offeringList = ruleList[0].offeringList;
		var ruleName = quotationItemList[index].Promotion_Rule_Name__c;
		if (promotionItem.promotion.Promotion_Type__c == "Price Break") {
			var availableRule = this.getAvailableRuleInPriceBreak(quotationItemList, index, promotionItem);
			if (availableRule) {
				offeringList = availableRule.offeringList;
			}
		}

		offeringList.forEach(function (oItem) {
			if (oItem.offering.RecordType.DeveloperName == "Specific_Free_Good") {
				var products = oItem.products;
				products.forEach(function (pItem) {
					for (var i = quotationItemList.length - 1; i >= 0; i--) {
						var qItem = quotationItemList[i];
						if (qItem.Promotion_Rule_Name__c == ruleName && qItem.isOffering && qItem.ProductCode__c == pItem.Product__r.ProductCode) {
							qItem.Quantity__c = 0;
							qItem.Promo_Discount_Amount__c = 0.0;
						}
					}
				});
			}
		});
	},
	addThresholdItems: function (quotationItemList, index, indexObj, component) {
		var quotationItem = quotationItemList[index];
		var promotionItem = quotationItemList[index].Promotion;
		var productCode = quotationItem.ProductCode__c;
		var ruleList = promotionItem.ruleList;
		var ruleName = ruleList[0].ruleName;
		var thresholdList = ruleList[0].thresholdList;
		var hasFreeGoods = this.hasFreeGoods(ruleList[0]);
		var currentIndex = indexObj.startindex;
		this.counterrorCheck(quotationItemList);
		// add haibo
        console.log(JSON.stringify(quotationItem), 'quotationItem==================');
		if (promotionItem.promotion.Promotion_Type__c == "Price Break") {
			var miniRule = this.getMiniRuleInPriceBreak(promotionItem);
			thresholdList = miniRule.thresholdList;
			ruleName = miniRule.ruleName;
			var availableRule = this.getAvailableRuleInPriceBreak(quotationItemList, index, promotionItem);
			if (availableRule) {
				thresholdList = availableRule.thresholdList;
				ruleName = availableRule.ruleName;
			}
		}
		quotationItem.Promotion_Rule_Name__c = ruleName;
		var times = this.getThresholdTimes(quotationItemList, index, promotionItem);
		if (times < 1) {
			times = 1;
		}
		thresholdList.forEach(function (tItem) {
			var products = tItem.products;
			var priceBook = tItem.priceBookEntrys;
			var pIndex = 0;
			var thresholdType = tItem.threshold.RecordType.DeveloperName;
			products.forEach(function (pItem) {
				if (pItem.Product__r.ProductCode == productCode) {
					if (thresholdType == "By_Quantity_of_Specific_Product") {
						console.log(JSON.stringify(pItem), 'pItem=================111');
						if (quotationItem.Quantity__c < pItem.Minimum_Quantity__c) {
							quotationItem.Quantity__c = pItem.Minimum_Quantity__c;
							if (pItem.Increment_For_Free_Goods__c && pItem.Increment_For_Free_Goods__c > 0) {
								times = Math.floor(quotationItem.Quantity__c / pItem.Increment_For_Free_Goods__c);
								console.log(times, 'times=================');
							}
						} else {
                            if (pItem.Increment_For_Free_Goods__c && Number(pItem.Increment_For_Free_Goods__c) != 1) {
                                quotationItem.Quantity__c = Math.ceil(Number( quotationItem.Quantity__c) / Number(pItem.Increment_For_Free_Goods__c)) * Number(pItem.Increment_For_Free_Goods__c);
                            }
                            if (pItem.Increment_For_Free_Goods__c && pItem.Increment_For_Free_Goods__c > 0) {
                                times = Math.floor(quotationItem.Quantity__c / pItem.Increment_For_Free_Goods__c);
                                console.log(times, 'times=================');
                            }
                        }
					} else if (thresholdType == "By_Amount_of_Specific_Product") {
						var minimumQty = Math.floor(pItem.Minimum_Amount__c / quotationItem.List_Price__c);
						if (minimumQty * quotationItem.List_Price__c < pItem.Minimum_Amount__c) {
							minimumQty += 1;
						}
						if (quotationItem.Quantity__c < minimumQty) {
							quotationItem.Quantity__c = minimumQty;
						}
						if (pItem.Maximum_Amount__c) {
							var maximumQty = Math.floor(pItem.Maximum_Amount__c / quotationItem.List_Price__c);
							if (maximumQty * quotationItem.List_Price__c == pItem.Maximum_Amount__c) {
								maximumQty -= 1;
							}
							if (quotationItem.Quantity__c >= maximumQty) {
								quotationItem.Quantity__c = maximumQty;
							}
						}
					} else if (thresholdType == "By_Mix_Match") {
						if (quotationItem.Quantity__c < pItem.Number__c) {
							quotationItem.Quantity__c = pItem.Number__c;
						}

						quotationItem.Additional_Discount__c = pItem.Additional_Discount__c ? pItem.Additional_Discount__c : 0;
						quotationItem.additionalDiscount = pItem.Additional_Discount__c ? pItem.Additional_Discount__c : 0;

						quotationItem.Additional_Amount_Off__c = pItem.Additional_Amount_Off__c ? pItem.Additional_Amount_Off__c : 0;
                        quotationItem.additionalAmountOff = pItem.Additional_Amount_Off__c ? pItem.Additional_Amount_Off__c : 0;

						if(quotationItem.additionalDiscount) {
                            quotationItem.Promo_Discount_Amount__c = Number(-(quotationItem.List_Price__c * quotationItem.Quantity__c) * (quotationItem.additionalDiscount/100));
                            quotationItem.Promo_Discount_Amount__c = Number(Number(quotationItem.Promo_Discount_Amount__c).toFixed(2));
                        }
                        else if(quotationItem.additionalAmountOff) {
                            quotationItem.Promo_Discount_Amount__c = Number(-(quotationItem.List_Price__c * quotationItem.Quantity__c) * (quotationItem.additionalAmountOff * quotationItem.Quantity__c));
                            quotationItem.Promo_Discount_Amount__c = Number(Number(quotationItem.Promo_Discount_Amount__c).toFixed(2));
                        }

						quotationItem.isMeet = true;
						quotationItem.isMix = true;
					} else if (thresholdType == "By_Full_Pallet_Quantity_of_Specific_Product") {
						var minimumQty = pItem.Minimum_Quantity__c * pItem.Product__r.Full_Pallet_Quantity__c;
						if (quotationItem.Quantity__c < minimumQty) {
							quotationItem.Quantity__c = minimumQty;
						}
					}

					quotationItem.Sub_Total__c = (quotationItem.List_Price__c * quotationItem.Quantity__c).toFixed(2);
				}

				if (thresholdType == "By_Mix_Match" && pItem.selected && pItem.Product__r.ProductCode != productCode) {
					var thresholdIndex = parseInt(index) + 1 + currentIndex;
					var thresholdPriceBookEntry = priceBook[pIndex];
					var unitPrice = 0.0;
					if (thresholdPriceBookEntry.UnitPrice) {
						unitPrice = thresholdPriceBookEntry.UnitPrice.toFixed(2);
					}
					var thresholdItem = {
						"Brand__c": pItem.Product__r.Brand_Name__c,
						"Product__c": pItem.Product__c,
						"Product__r": {
							"SF_Description__c": pItem.Product__r.SF_Description__c
						},
						"ProductCode__c": pItem.Product__r.ProductCode,
						"Ship_Date__c": quotationItem.Ship_Date__c,
						"Quantity__c": pItem.Number__c * times,
						"Price_Book__c": thresholdPriceBookEntry.Pricebook2Id,
						"List_Price__c": unitPrice,
						"Unit_Price__c": unitPrice,
						"Discount_Amount__c": 0.0,
						"Promo_Discount_Amount__c": 0.0,
						"Whole_Order_Promo_Discount_Amount__c": 0.0,
						"Additional_Discount__c": pItem.Additional_Discount__c ? pItem.Additional_Discount__c : 0,
						"additionalDiscount": pItem.Additional_Discount__c ? pItem.Additional_Discount__c : 0,
						"Additional_Amount_Off__c": pItem.Additional_Amount_Off__c ? pItem.Additional_Amount_Off__c : 0,
                        "additionalAmountOff": pItem.Additional_Amount_Off__c ? pItem.Additional_Amount_Off__c : 0,
						"Sub_Total__c": (unitPrice * pItem.Number__c * times).toFixed(2),
						"PromotionName__c": promotionItem.promotion.Promotion_Code_For_External__c,
						"Promotion__c": promotionItem.promotion.Id,
						"Regular_Promotion_Window__c": promotionItem.windowId,
						"Promotion_Rule_Name__c": ruleName,
						"isOffering": false,
						"isThreshold": true,
						"HasPromo": false,
						"HasDiscount": false,
						"promotionList": [promotionItem],
						"isMix": true,
						"isMeet": true,
						"Promotion": promotionItem
					};
					var orgcode = component.get("v.OrgCode");
					if (orgcode != "CCA") {
						thresholdItem.CS_Exchange_Rate__c = pItem.Product__r.CS_Exchange_Rate__c ? pItem.Product__r.CS_Exchange_Rate__c : 1;
					} else {
						thresholdItem.CS_Exchange_Rate__c = 1;
					}
					if (thresholdItem.additionalDiscount) {
						thresholdItem.Promo_Discount_Amount__c = Number(-(thresholdItem.List_Price__c * thresholdItem.Quantity__c) * (thresholdItem.additionalDiscount / 100));
						thresholdItem.Promo_Discount_Amount__c = Number(Number(thresholdItem.Promo_Discount_Amount__c).toFixed(2));
					}
					if(thresholdItem.additionalAmountOff) {
                        thresholdItem.Promo_Discount_Amount__c = Number(-(thresholdItem.List_Price__c * thresholdItem.Quantity__c - thresholdItem.additionalAmountOff * thresholdItem.Quantity__c));
                        thresholdItem.Promo_Discount_Amount__c = Number(Number(thresholdItem.Promo_Discount_Amount__c).toFixed(2));
                    }
					quotationItemList.splice(thresholdIndex, 0, thresholdItem);
					currentIndex++;
				}

				if (thresholdType == "By_Quantity_of_Specific_Product" && pItem.Product__r.ProductCode != productCode) {
					var thresholdIndex = parseInt(index) + 1 + currentIndex;
					var thresholdPriceBookEntry = priceBook[pIndex];
					var unitPrice = 0.0;
					if (thresholdPriceBookEntry.UnitPrice) {
						unitPrice = thresholdPriceBookEntry.UnitPrice.toFixed(2);
					}
					var minimumQty = 0;
					if (pItem.Increment_For_Free_Goods__c) {
						minimumQty = pItem.Increment_For_Free_Goods__c * times;
						if (minimumQty < pItem.Minimum_Quantity__c) {
							minimumQty = pItem.Minimum_Quantity__c;
						}
					} else {
						minimumQty = pItem.Minimum_Quantity__c * times;
					}
					if (!hasFreeGoods) {
						minimumQty = pItem.Minimum_Quantity__c;
					}
					var thresholdItem = {
						"Brand__c": pItem.Product__r.Brand_Name__c,
						"Product__c": pItem.Product__c,
						"Product__r": {
							"SF_Description__c": pItem.Product__r.SF_Description__c
						},
						"ProductCode__c": pItem.Product__r.ProductCode,
						"Ship_Date__c": quotationItem.Ship_Date__c,
						"Quantity__c": minimumQty,
						"Price_Book__c": thresholdPriceBookEntry.Pricebook2Id,
						"List_Price__c": unitPrice,
						"Unit_Price__c": unitPrice,
						"Discount_Amount__c": 0.0,
						"Promo_Discount_Amount__c": 0.0,
						"Whole_Order_Promo_Discount_Amount__c": 0.0,
						"Sub_Total__c": (unitPrice * minimumQty).toFixed(2),
						"PromotionName__c": promotionItem.promotion.Promotion_Code_For_External__c,
						"Promotion__c": promotionItem.promotion.Id,
						"Regular_Promotion_Window__c": promotionItem.windowId,
						"Promotion_Rule_Name__c": ruleName,
						"isOffering": false,
						"isThreshold": true,
						"HasPromo": false,
						"HasDiscount": false,
						"promotionList": [promotionItem],
						"isMix": false,
						"hasFreeGoods": hasFreeGoods,
						"Promotion": promotionItem
					};
					var orgcode = component.get("v.OrgCode");
					if (orgcode != "CCA") {
						thresholdItem.CS_Exchange_Rate__c = pItem.Product__r.CS_Exchange_Rate__c ? pItem.Product__r.CS_Exchange_Rate__c : 1;
					} else {
						thresholdItem.CS_Exchange_Rate__c = 1;
					}
					quotationItemList.splice(thresholdIndex, 0, thresholdItem);
					currentIndex++;
				}
				if (thresholdType == "By_Amount_of_Specific_Product" && pItem.Product__r.ProductCode != productCode) {
					var thresholdIndex = parseInt(index) + 1 + currentIndex;
					var thresholdPriceBookEntry = priceBook[pIndex];
					var unitPrice = 0.0;
					if (thresholdPriceBookEntry.UnitPrice) {
						unitPrice = thresholdPriceBookEntry.UnitPrice.toFixed(2);
					}
					var minimumQty = Math.floor((pItem.Minimum_Amount__c * times) / unitPrice);
					if (!hasFreeGoods) {
						minimumQty = Math.floor(pItem.Minimum_Amount__c / unitPrice);
					}
					if (minimumQty * unitPrice < pItem.Minimum_Amount__c * times) {
						minimumQty += 1;
					}
					var thresholdItem = {
						"Brand__c": pItem.Product__r.Brand_Name__c,
						"Product__c": pItem.Product__c,
						"Product__r": {
							"SF_Description__c": pItem.Product__r.SF_Description__c
						},
						"ProductCode__c": pItem.Product__r.ProductCode,
						"Ship_Date__c": quotationItem.Ship_Date__c,
						"Quantity__c": minimumQty,
						"Price_Book__c": thresholdPriceBookEntry.Pricebook2Id,
						"List_Price__c": unitPrice,
						"Unit_Price__c": unitPrice,
						"Discount_Amount__c": 0.0,
						"Promo_Discount_Amount__c": 0.0,
						"Whole_Order_Promo_Discount_Amount__c": 0.0,
						"Sub_Total__c": (unitPrice * minimumQty).toFixed(2),
						"PromotionName__c": promotionItem.promotion.Promotion_Code_For_External__c,
						"Promotion__c": promotionItem.promotion.Id,
						"Regular_Promotion_Window__c": promotionItem.windowId,
						"Promotion_Rule_Name__c": ruleName,
						"isOffering": false,
						"isThreshold": true,
						"HasPromo": false,
						"HasDiscount": false,
						"promotionList": [promotionItem],
						"hasFreeGoods": hasFreeGoods,
						"isMix": false,
						"Promotion": promotionItem
					};
					var orgcode = component.get("v.OrgCode");
					if (orgcode != "CCA") {
						thresholdItem.CS_Exchange_Rate__c = pItem.Product__r.CS_Exchange_Rate__c ? pItem.Product__r.CS_Exchange_Rate__c : 1;
					} else {
						thresholdItem.CS_Exchange_Rate__c = 1;
					}
					quotationItemList.splice(thresholdIndex, 0, thresholdItem);
					currentIndex++;
				}
				if (thresholdType == "By_Full_Pallet_Quantity_of_Specific_Product" && pItem.Product__r.ProductCode != productCode) {
					var thresholdIndex = parseInt(index) + 1 + currentIndex;
					var thresholdPriceBookEntry = priceBook[pIndex];
					var unitPrice = 0.0;
					if (thresholdPriceBookEntry.UnitPrice) {
						unitPrice = thresholdPriceBookEntry.UnitPrice.toFixed(2);
					}
					var minimumQty = pItem.Minimum_Quantity__c * pItem.Product__r.Full_Pallet_Quantity__c * times;
					if (!hasFreeGoods) {
						minimumQty = pItem.Minimum_Quantity__c * pItem.Product__r.Full_Pallet_Quantity__c;
					}
					var thresholdItem = {
						"Brand__c": pItem.Product__r.Brand_Name__c,
						"Product__c": pItem.Product__c,
						"Product__r": {
							"SF_Description__c": pItem.Product__r.SF_Description__c
						},
						"ProductCode__c": pItem.Product__r.ProductCode,
						"Ship_Date__c": quotationItem.Ship_Date__c,
						"Quantity__c": minimumQty,
						"Price_Book__c": thresholdPriceBookEntry.Pricebook2Id,
						"List_Price__c": unitPrice,
						"Unit_Price__c": unitPrice,
						"Discount_Amount__c": 0.0,
						"Promo_Discount_Amount__c": 0.0,
						"Whole_Order_Promo_Discount_Amount__c": 0.0,
						"Sub_Total__c": (unitPrice * minimumQty).toFixed(2),
						"PromotionName__c": promotionItem.promotion.Promotion_Code_For_External__c,
						"Promotion__c": promotionItem.promotion.Id,
						"Regular_Promotion_Window__c": promotionItem.windowId,
						"Promotion_Rule_Name__c": ruleName,
						"isOffering": false,
						"isThreshold": true,
						"HasPromo": false,
						"HasDiscount": false,
						"promotionList": [promotionItem],
						"hasFreeGoods": hasFreeGoods,
						"isMix": false,
						"Promotion": promotionItem
					};
					var orgcode = component.get("v.OrgCode");
					if (orgcode != "CCA") {
						thresholdItem.CS_Exchange_Rate__c = pItem.Product__r.CS_Exchange_Rate__c ? pItem.Product__r.CS_Exchange_Rate__c : 1;
					} else {
						thresholdItem.CS_Exchange_Rate__c = 1;
					}
					quotationItemList.splice(thresholdIndex, 0, thresholdItem);
					currentIndex++;
				}
				pIndex++;
			});
		});
		indexObj.startindex = currentIndex;
		// this.counterrorCheck(quotationItemList);
	},
	removePromotionQuotationItem: function (quotationItemList, index, component) {
		var quotationItem = quotationItemList[index];
		var ruleName = quotationItemList[index].Promotion_Rule_Name__c;
		var productCode = quotationItem.ProductCode__c;
		var promotionCode = quotationItemList[index].PromotionName__c;
		var index = quotationItemList.length - 1;
		var recordId = quotationItem.Id;
		if (recordId) {
			var recordIds = [];
			quotationItemList.forEach(function (qItem) {
				if (qItem.Promotion_Rule_Name__c == ruleName && qItem.Id && qItem.Id != recordId) {
					recordIds.push(qItem.Id);
				}
			});
			if (recordIds.length > 0) {
				var action = component.get("c.deleteQuotation");
				action.setParams({
					"recordIds": recordIds
				});
				action.setCallback(this, function (response) {
					var state = response.getState();
					if (state === "SUCCESS") {
						var results = response.getReturnValue();
					} else {
						var errors = response.getError();
						if (errors) {
							if (errors[0] && errors[0].message) {
								alert("ERROR: " + errors[0].message);
							}
						} else {
							alert("ERROR: Unknown error");
						}
					}
				});
				$A.enqueueAction(action);
			}
		}

		for (; index >= 0; index--) {
			var qItem = quotationItemList[index];
			if (qItem.Promotion_Rule_Name__c == ruleName && !qItem.HasPromo) {
				quotationItemList.splice(index, 1);
			} else if (qItem.Promotion_Rule_Name__c == ruleName && qItem.HasPromo) {
				//quotationItem.Discount_Amount__c = 0.00;
				quotationItem.Promo_Discount_Amount__c = 0.0;
				quotationItem.Whole_Order_Promo_Discount_Amount__c = 0.0;
				quotationItem.Sub_Total__c = (Number(quotationItem.List_Price__c * quotationItem.Quantity__c) + Number(quotationItem.Discount_Amount__c)).toFixed(2);
				quotationItem.Unit_Price__c = quotationItem.List_Price__c;
			}
		}
	},
	isPoolFreeGoods: function (ruleItem) {
		var offeringList = ruleItem.offeringList;
		var hasPool = false;
		offeringList.forEach(function (oItem) {
			if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
				hasPool = true;
			}
		});
		return hasPool;
	},
	clearSelectedProducts: function (selectedPromotion) {
		var thresholdList = selectedPromotion.ruleList[0].thresholdList;
		if (this.hasMixMatch(selectedPromotion)) {
			thresholdList.forEach(function (tItem) {
				if (tItem.threshold.RecordType.DeveloperName == "By_Mix_Match") {
					var products = tItem.products;
					products.forEach(function (pItem) {
						pItem.selected = false;
						pItem.hidden = false;
					});
				}
			});
		}
		var ruleList = selectedPromotion.ruleList;
		ruleList.forEach(function (rItem) {
			var offeringList = rItem.offeringList;
			offeringList.forEach(function (oItem) {
				if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
					var products = oItem.products;
					products.forEach(function (pItem) {
						pItem.selected = false;
						pItem.hidden = false;
					});
				}
			});
		});
	},
	clearPromotionStatus: function (quotationItemList, index) {
		var quotationItem = quotationItemList[index];
		//quotationItem.HasPromo = false;
		quotationItem.hasPool = false;
		quotationItem.hasMix = false;
		quotationItem.isPool = false;
		quotationItem.isMix = false;
		quotationItem.isThreshold = false;
		quotationItem.isOffering = false;
		quotationItem.isLess = false;
		quotationItem.PromotionName__c = undefined;
		quotationItem.Promotion__c = undefined;
		quotationItem.Regular_Promotion_Window__c = undefined;
		quotationItem.Promotion = undefined;
		quotationItem.Promotion_Rule_Name__c = undefined;
		quotationItem.Promo_Discount_Amount__c = 0.0;
		quotationItem.Whole_Order_Promo_Discount_Amount__c = 0.0;
		//quotationItem.promotionList = undefined;
		if (quotationItem.Quantity__c && quotationItem.Quantity__c > 0) {
			quotationItem.Sub_Total__c =
				Number(quotationItem.List_Price__c * quotationItem.Quantity__c) +
				Number(quotationItem.Discount_Amount__c) +
				Number(quotationItem.Promo_Discount_Amount__c) +
				Number(quotationItem.Whole_Order_Promo_Discount_Amount__c);
			quotationItem.Unit_Price__c = Number(quotationItem.Sub_Total__c / quotationItem.Quantity__c).toFixed(2);
		} else {
			quotationItem.Sub_Total__c = 0.0;
			quotationItem.Unit_Price__c = quotationItem.List_Price__c;
		}
	},
	initNotApplyPromoList: function (quotationItemList) {
		var notApplyPromoList = [];
		var index = 1;
		quotationItemList.forEach(function (qItem) {
			if (qItem.HasPromo && !qItem.Promotion) {
				var notApplyPromoItem = {
					"name": "Order Line " + index + ":",
					"promoList": qItem.promotionList
				};
				notApplyPromoList.push(notApplyPromoItem);
			}
			index++;
		});
		return notApplyPromoList;
	},
	isMeetThreshold: function (quotationItemList, index, promotion) {
		var isMeet = true;
		// var times = this.getThresholdTimes(quotationItemList, index, promotion);
		// if(times >= 1){
		//     isMeet = true;
		// }
		var currentQuotation = quotationItemList[index];
		var currentProductCode = currentQuotation.ProductCode__c;
		var currentRuleName = currentQuotation.Promotion_Rule_Name__c;
		var currentQty = currentQuotation.Quantity__c;
		var currentAmt = currentQuotation.List_Price__c * currentQuotation.Quantity__c;
		if (promotion.promotion.Promotion_Type__c == "Price Break") {
			var availableRule = this.getAvailableRuleInPriceBreak(quotationItemList, index, promotion);
			if (!availableRule) {
				isMeet = false;
			}
		} else {
			var thresholdList = promotion.ruleList[0].thresholdList;
			thresholdList.forEach(function (tItem) {
				if (tItem.threshold.RecordType.DeveloperName == "By_Quantity_of_Specific_Product") {
					quotationItemList.forEach(function (qItem) {
						if (qItem.ProductCode__c == tItem.products[0].Product__r.ProductCode && qItem.Promotion_Rule_Name__c == currentRuleName && (qItem.isThreshold || qItem.HasPromo)) {
							var minimumQty = tItem.products[0].Minimum_Quantity__c;
							if (qItem.Quantity__c < minimumQty) {
								isMeet = false;
							}
						}
					});
				} else if (tItem.threshold.RecordType.DeveloperName == "By_Amount_of_Specific_Product") {
					quotationItemList.forEach(function (qItem) {
						if (qItem.ProductCode__c == tItem.products[0].Product__r.ProductCode && qItem.Promotion_Rule_Name__c == currentRuleName && (qItem.isThreshold || qItem.HasPromo)) {
							var minimumAmt = tItem.products[0].Minimum_Amount__c;
							var qAmt = qItem.List_Price__c * qItem.Quantity__c;
							if (qAmt < minimumAmt) {
								isMeet = false;
							}
						}
					});
				} else if (tItem.threshold.RecordType.DeveloperName == "By_Full_Pallet_Quantity_of_Specific_Product") {
					quotationItemList.forEach(function (qItem) {
						if (qItem.ProductCode__c == tItem.products[0].Product__r.ProductCode && qItem.Promotion_Rule_Name__c == currentRuleName && (qItem.isThreshold || qItem.HasPromo)) {
							var minimumQty = tItem.products[0].Minimum_Quantity__c * tItem.products[0].Product__r.Full_Pallet_Quantity__c;
							if (qItem.Quantity__c < minimumQty) {
								isMeet = false;
							}
						}
					});
				} else if (tItem.threshold.RecordType.DeveloperName == "By_Mix_Match") {
					var products = tItem.products;
					var miniTotalQty = tItem.threshold.Minimum_Total_Quantity__c;
					var miniTotalAmount = tItem.threshold.Minimum_Total_Amount__c;
					var miniDiffModels = tItem.threshold.Minimum_Different_Tool_Models__c;
					var maxDiffModels = tItem.threshold.Max_Different_Tool_Models__c;
					var selectedDiffModels = 0;
					var selectedTotalQty = 0;
					var selectedTotalAmount = 0.0;
					products.forEach(function (pItem) {
						var productCode = pItem.Product__r.ProductCode;
						var miniQty = pItem.Number__c;
						if (pItem.selected) {
							selectedDiffModels++;
							quotationItemList.forEach(function (qItem) {
								if (qItem.ProductCode__c == productCode && qItem.Promotion_Rule_Name__c == currentRuleName && (qItem.isThreshold || qItem.HasPromo)) {
									selectedTotalAmount += Number(qItem.List_Price__c) * Number(qItem.Quantity__c);
									selectedTotalQty += Number(qItem.Quantity__c);
									if (qItem.Quantity__c < miniQty) {
										isMeet = false;
									}
								}
							});
						}
					});
					if (selectedDiffModels < miniDiffModels) {
						isMeet = false;
					}
					if (maxDiffModels && selectedDiffModels > maxDiffModels) {
						isMeet = false;
					}
					if (miniTotalAmount > 0 && selectedTotalAmount < miniTotalAmount) {
						isMeet = false;
					}
					if (miniTotalQty > 0 && selectedTotalQty < miniTotalQty) {
						isMeet = false;
					}
				}
			});
		}
		return isMeet;
	},
	isMeetWholeOrderPromo: function (quotationItemList, wholeOrderPromo) {
		var isMeetWholeOrderPromo = false;
		var totalAmt = 0.0;
		var totalQuantity = 0;
		quotationItemList.forEach(function (qItem) {
			if (!qItem.isOffering && qItem.Product__c) {
				var qAmount = Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Promo_Discount_Amount__c) + Number(qItem.Discount_Amount__c);
				totalAmt += qAmount;
				totalQuantity += Number(qItem.Quantity__c);
			}
		});

		var threshold = wholeOrderPromo.ruleList[0].thresholdList[0].threshold;
		if (threshold.RecordType.DeveloperName == "By_Quantity_of_Whole_Order") {
			if (totalQuantity >= threshold.Minimum_Whole_Order_Quantity__c) {
				isMeetWholeOrderPromo = true;
			}
		} else if (threshold.RecordType.DeveloperName == "By_Amount_of_Whole_Order") {
			if (totalAmt >= threshold.Minimum_Whole_Order_Amount__c) {
				isMeetWholeOrderPromo = true;
			}
		}
		return isMeetWholeOrderPromo;
	},
	calculateWithWholeOrderPromo: function (quotationItemList, wholeOrderPromo, component) {
		var totalAmt = 0.0;
		var totalQuantity = 0;
		quotationItemList.forEach(function (qItem) {
			if (!qItem.isOffering && qItem.Product__c) {
				var qAmount = Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Promo_Discount_Amount__c) + Number(qItem.Discount_Amount__c);
				//var qAmount = Number(qItem.List_Price__c * qItem.Quantity__c); pItem
				totalAmt += qAmount;
				totalQuantity += Number(qItem.Quantity__c);
			}
		});

		var offeringList = wholeOrderPromo.ruleList[0].offeringList;
		var times = this.getWholeOrderPromotionThresholdTimes(quotationItemList, wholeOrderPromo);
		offeringList.forEach(function (oItem) {
			if (oItem.offering.RecordType.DeveloperName == "Amount_Off") {
				var totalAmoutOff = oItem.offering.Amount_Off__c * times;
				console.log("oItem.offering.Amount_Off__c" + oItem.offering.Amount_Off__c);
				console.log("times" + times);
				quotationItemList.forEach(function (qItem) {
					if (qItem.Is_Initial__c || qItem.isThreshold) {
						qItem.Whole_Order_Promotion__c = wholeOrderPromo.promotion.Id;
						qItem.Whole_Order_Promo_Discount_Amount__c = Number(
							Number(((Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Discount_Amount__c) + Number(qItem.Promo_Discount_Amount__c)) / totalAmt) * -totalAmoutOff).toFixed(
								2
							)
						);
						//qItem.Whole_Order_Promo_Discount_Amount__c = Number((Number(qItem.List_Price__c * qItem.Quantity__c)/totalAmt * (-totalAmoutOff)).toFixed(2));
						if (qItem.Quantity__c && qItem.Quantity__c > 0) {
							qItem.Sub_Total__c = (
								Number(qItem.List_Price__c) * Number(qItem.Quantity__c) +
								Number(qItem.Promo_Discount_Amount__c) +
								Number(qItem.Discount_Amount__c) +
								Number(qItem.Whole_Order_Promo_Discount_Amount__c)
							).toFixed(2);
							qItem.Unit_Price__c = Number(qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2);
						} else {
							qItem.Sub_Total__c = 0.0;
							qItem.Unit_Price__c = qItem.List_Price__c;
						}
					} else if (qItem.isOffering) {
						qItem.Whole_Order_Promotion__c = wholeOrderPromo.promotion.Id;
						qItem.Whole_Order_Promo_Discount_Amount__c = 0.0;
					}
				});
			} else if (oItem.offering.RecordType.DeveloperName == "Discount_Off") {
				var discountOff = oItem.offering.Discount_Off__c;
				quotationItemList.forEach(function (qItem) {
					if (qItem.Is_Initial__c || qItem.isThreshold) {
						qItem.Whole_Order_Promotion__c = wholeOrderPromo.promotion.Id;
						qItem.Whole_Order_Promo_Discount_Amount__c = Number(
							((Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Discount_Amount__c) + Number(qItem.Promo_Discount_Amount__c)) * (-discountOff / 100)).toFixed(2)
						);
						//qItem.Whole_Order_Promo_Discount_Amount__c = Number(((Number(qItem.List_Price__c * qItem.Quantity__c)) * (-discountOff/100)).toFixed(2));

						if (qItem.Quantity__c && qItem.Quantity__c > 0) {
							qItem.Sub_Total__c = (
								Number(qItem.List_Price__c) * Number(qItem.Quantity__c) +
								Number(qItem.Promo_Discount_Amount__c) +
								Number(qItem.Discount_Amount__c) +
								Number(qItem.Whole_Order_Promo_Discount_Amount__c)
							).toFixed(2);
							qItem.Unit_Price__c = Number(qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2);
						} else {
							qItem.Sub_Total__c = 0.0;
							qItem.Unit_Price__c = qItem.List_Price__c;
						}
					} else if (qItem.isOffering) {
						qItem.Whole_Order_Promotion__c = wholeOrderPromo.promotion.Id;
						qItem.Whole_Order_Promo_Discount_Amount__c = 0.0;
					}
				});
			} else if (oItem.offering.RecordType.DeveloperName == "Specific_Free_Good") {
				var products = oItem.products;
				var priceBookEntrys = oItem.priceBookEntrys;
				if (products) {
					var pIndex = 0;
					products.forEach(function (element) {
						var offeringProduct = products[pIndex];
						var offeringPriceBookEntry = priceBookEntrys[pIndex];
						var unitPrice = 0.0;
						if (offeringPriceBookEntry.UnitPrice) {
							unitPrice = offeringPriceBookEntry.UnitPrice.toFixed(2);
						}
						var qty = 1;
						if (offeringProduct.Gift_Quantity__c) {
							qty = times * offeringProduct.Gift_Quantity__c;
						}
						var offeringItem = {
							"Brand__c": offeringProduct.Product__r.Brand_Name__c,
							"Product__c": offeringProduct.Product__c,
							"Product__r": {
								"SF_Description__c": offeringProduct.Product__r.SF_Description__c
							},
							"ProductCode__c": offeringProduct.Product__r.ProductCode,
							"Ship_Date__c": component.get("v.quotation.Expected_Delivery_Date__c"),
							"Quantity__c": qty,
							// "Quantity__c" : 1,
							"Price_Book__c": offeringPriceBookEntry.Pricebook2Id,
							"List_Price__c": unitPrice,
							"Unit_Price__c": 0.0,
							"Discount_Amount__c": 0.0,
							"Promo_Discount_Amount__c": 0.0,
							"Whole_Order_Promo_Discount_Amount__c": -unitPrice * qty,
							"Sub_Total__c": 0.0,
							"PromotionName__c": wholeOrderPromo.promotion.Promotion_Code_For_External__c,
							"Promotion__c": wholeOrderPromo.promotion.Id,
							"Regular_Promotion_Window__c": wholeOrderPromo.windowId,
							"Whole_Order_Promotion__c": wholeOrderPromo.promotion.Id,
							"isOffering": true,
							"HasPromo": false,
							"HasDiscount": true,
							"isPool": false
						};
						for (var i = 0; i < quotationItemList.length; i++) {
							if (quotationItemList[i].ProductCode__c == offeringItem.ProductCode__c) {
								offeringItem.counterror = quotationItemList[i].counterror;
							}
						}
						var orgcode = component.get("v.OrgCode");
						if (orgcode != "CCA") {
							offeringItem.CS_Exchange_Rate__c = offeringProduct.Product__r.CS_Exchange_Rate__c ? offeringProduct.Product__r.CS_Exchange_Rate__c : 1;
						} else {
							offeringItem.CS_Exchange_Rate__c = 1;
						}
						quotationItemList.push(offeringItem);
						pIndex++;
					});
				}
			} else if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
				var products = oItem.products;
				var priceBookEntrys = oItem.priceBookEntrys;
				if (products) {
					var pIndex = 0;
					products.forEach(function (pItem) {
						if (pItem.selected) {
							var offeringProduct = products[pIndex];
							var offeringPriceBookEntry = priceBookEntrys[pIndex];
							var unitPrice = 0.0;
							if (offeringPriceBookEntry.UnitPrice) {
								unitPrice = offeringPriceBookEntry.UnitPrice.toFixed(2);
							}
							var qty = 1;
							if (offeringProduct.Number__c) {
								qty = times * offeringProduct.Number__c;
							}
							var offeringItem = {
								"Brand__c": offeringProduct.Product__r.Brand_Name__c,
								"Product__c": offeringProduct.Product__c,
								"Product__r": {
									"SF_Description__c": offeringProduct.Product__r.SF_Description__c
								},
								"ProductCode__c": offeringProduct.Product__r.ProductCode,
								"Ship_Date__c": component.get("v.quotation.Expected_Delivery_Date__c"),
								"Quantity__c": 1,
								// "Quantity__c" : qty,
								"Price_Book__c": offeringPriceBookEntry.Pricebook2Id,
								"List_Price__c": unitPrice,
								"Unit_Price__c": 0.0,
								"Discount_Amount__c": 0.0,
								"Promo_Discount_Amount__c": 0.0,
								"Whole_Order_Promo_Discount_Amount__c": -unitPrice * qty,
								"Sub_Total__c": 0.0,
								"PromotionName__c": wholeOrderPromo.promotion.Promotion_Code_For_External__c,
								"Promotion__c": wholeOrderPromo.promotion.Id,
								"Regular_Promotion_Window__c": wholeOrderPromo.windowId,
								"Whole_Order_Promotion__c": wholeOrderPromo.promotion.Id,
								"isOffering": true,
								"HasPromo": false,
								"HasDiscount": true,
								"isPool": true,
								"Promotion": wholeOrderPromo
							};
							for (var i = 0; i < quotationItemList.length; i++) {
								if (quotationItemList[i].ProductCode__c == offeringItem.ProductCode__c) {
									offeringItem.counterror = quotationItemList[i].counterror;
								}
							}
							var orgcode = component.get("v.OrgCode");
							if (orgcode != "CCA") {
								offeringItem.CS_Exchange_Rate__c = offeringProduct.Product__r.CS_Exchange_Rate__c ? offeringProduct.Product__r.CS_Exchange_Rate__c : 1;
							} else {
								offeringItem.CS_Exchange_Rate__c = 1;
							}
							quotationItemList.push(offeringItem);
						}
						pIndex++;
					});
				}
			}
		});
	},
	removeWholeOrderPromotionOfferingItem: function (quotationItemList, wholeOrderPromo, component) {
		var promotionCode = wholeOrderPromo.promotion.Promotion_Code_For_External__c;
		var recordIds = [];
		quotationItemList.forEach(function (qItem) {
			if (!qItem.isOffering && qItem.Product__c) {
				qItem.Whole_Order_Promotion__c = null;
				qItem.Whole_Order_Promo_Discount_Amount__c = 0.0;
				if (qItem.Quantity__c && qItem.Quantity__c > 0) {
					qItem.Sub_Total__c = (
						Number(qItem.List_Price__c) * Number(qItem.Quantity__c) +
						Number(qItem.Promo_Discount_Amount__c) +
						Number(qItem.Discount_Amount__c) +
						Number(qItem.Whole_Order_Promo_Discount_Amount__c)
					).toFixed(2);
					qItem.Unit_Price__c = Number(qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2);
				} else {
					qItem.Sub_Total__c = 0.0;
					qItem.Unit_Price__c = qItem.List_Price__c;
				}
			} else if (qItem.isOffering && qItem.PromotionName__c == promotionCode && qItem.Id) {
				recordIds.push(qItem.Id);
			} else if (qItem.isOffering && qItem.PromotionName__c != promotionCode) {
				qItem.Whole_Order_Promotion__c = null;
				qItem.Whole_Order_Promo_Discount_Amount__c = 0.0;
			}
		});

		//delete the offering records of whole order promotion from db.
		if (recordIds.length > 0) {
			var action = component.get("c.deleteQuotation");
			action.setParams({
				"recordIds": recordIds
			});
			action.setCallback(this, function (response) {
				var state = response.getState();
				if (state === "SUCCESS") {
					var results = response.getReturnValue();
				} else {
					var errors = response.getError();
					if (errors) {
						if (errors[0] && errors[0].message) {
							alert("ERROR: " + errors[0].message);
						}
					} else {
						alert("ERROR: Unknown error");
					}
				}
			});
			$A.enqueueAction(action);
		}

		//delete the offering items of whole order promotion from quotation list.
		var index = quotationItemList.length - 1;
		for (; index >= 0; index--) {
			var qItem = quotationItemList[index];
			if (qItem.PromotionName__c == promotionCode && qItem.isOffering) {
				quotationItemList.splice(index, 1);
			}
		}
	},
	isMeetPaymentTermPromo: function (quotationItemList, termPromo) {
		var isMeetPaymentTermPromo = false;
		var totalAmt = 0.0;
		var totalQuantity = 0;
		quotationItemList.forEach(function (qItem) {
			if (!qItem.isOffering && qItem.Product__c) {
				// var qAmount = Number(qItem.List_Price__c * qItem.Quantity__c)
				//             + Number(qItem.Promo_Discount_Amount__c)
				//             + Number(qItem.Discount_Amount__c);
				var qAmount = Number(qItem.List_Price__c * qItem.Quantity__c);
				totalAmt += qAmount;
				totalQuantity += Number(qItem.Quantity__c);
			}
		});

		var threshold = termPromo.ruleList[0].thresholdList[0].threshold;
		if (threshold.RecordType.DeveloperName == "By_Quantity_of_Whole_Order") {
			if (totalQuantity >= threshold.Minimum_Whole_Order_Quantity__c) {
				isMeetPaymentTermPromo = true;
			}
		} else if (threshold.RecordType.DeveloperName == "By_Amount_of_Whole_Order") {
			if (totalAmt >= threshold.Minimum_Whole_Order_Amount__c) {
				isMeetPaymentTermPromo = true;
			}
		}
		return isMeetPaymentTermPromo;
	},
	getThresholdTimes: function (quotationItemList, index, promotion) {
		var times = -1;
		var currentQuotation = quotationItemList[index];
		var currentProductCode = currentQuotation.ProductCode__c;
		var currentRuleName = currentQuotation.Promotion_Rule_Name__c;
		var currentQty = currentQuotation.Quantity__c;
		var currentAmt = currentQuotation.List_Price__c * currentQuotation.Quantity__c;

		if (promotion.promotion.Promotion_Type__c == "BOGO") {
			//only have one rule
			var thresholdList = promotion.ruleList[0].thresholdList;
			thresholdList.forEach(function (tItem) {
				if (tItem.threshold.RecordType.DeveloperName == "By_Quantity_of_Specific_Product" && tItem.products[0].Product__r.ProductCode == currentProductCode) {
					//var minimumQty = tItem.products[0].Minimum_Quantity__c;
					var minimumQty = tItem.products[0].Increment_For_Free_Goods__c;
					if (currentQty < minimumQty) {
						times = 0;
					} else {
						times = Math.floor(currentQty / minimumQty);
					}
				}
			});
		} else if (promotion.promotion.Promotion_Type__c == "Price Discount") {
			//only have one rule
			var thresholdList = promotion.ruleList[0].thresholdList;
			thresholdList.forEach(function (tItem) {
				if (tItem.threshold.RecordType.DeveloperName == "By_Quantity_of_Specific_Product") {
					var minimumQty = tItem.products[0].Minimum_Quantity__c;
					//times = Math.floor(currentQty/minimumQty);
					var productCode = tItem.products[0].Product__r.ProductCode;
					quotationItemList.forEach(function (qItem) {
						if (qItem.ProductCode__c == productCode && qItem.Promotion_Rule_Name__c == currentRuleName && (qItem.isThreshold || qItem.HasPromo)) {
							var qty = Number(qItem.Quantity__c);
							var currentTimes = Math.floor(qty / minimumQty);
							if (times == -1) {
								times = currentTimes;
							} else if (times > currentTimes) {
								times = currentTimes;
							}
						}
					});
				} else if (tItem.threshold.RecordType.DeveloperName == "By_Amount_of_Specific_Product") {
					var minimumAmt = tItem.products[0].Minimum_Amount__c;
					//times = Math.floor(currentAmt/minimumAmt);
					var productCode = tItem.products[0].Product__r.ProductCode;
					quotationItemList.forEach(function (qItem) {
						if (qItem.ProductCode__c == productCode && qItem.Promotion_Rule_Name__c == currentRuleName && (qItem.isThreshold || qItem.HasPromo)) {
							var amt = Number(qItem.List_Price__c) * Number(qItem.Quantity__c);
							var currentTimes = Math.floor(amt / minimumAmt);
							if (times == -1) {
								times = currentTimes;
							} else if (times > currentTimes) {
								times = currentTimes;
							}
						}
					});
				}
			});
		} else if (promotion.promotion.Promotion_Type__c == "Mix & Match") {
			//only have one rule
			var thresholdList = promotion.ruleList[0].thresholdList;
			thresholdList.forEach(function (tItem) {
				var products = tItem.products;
				if (tItem.threshold.RecordType.DeveloperName == "By_Mix_Match") {
					var miniTotalQty = tItem.threshold.Minimum_Total_Quantity__c;
					var miniTotalAmount = tItem.threshold.Minimum_Total_Amount__c;
					var miniDiffModels = tItem.threshold.Minimum_Different_Tool_Models__c;
					var selectedDiffModels = 0;
					var selectedTotalQty = 0;
					var selectedTotalAmount = 0.0;
					products.forEach(function (pItem) {
						var productCode = pItem.Product__r.ProductCode;
						var miniQty = pItem.Number__c;
						if (pItem.selected) {
							selectedDiffModels++;
							quotationItemList.forEach(function (qItem) {
								if (qItem.ProductCode__c == productCode && qItem.Promotion_Rule_Name__c == currentRuleName && (qItem.isThreshold || qItem.HasPromo)) {
									selectedTotalAmount += Number(qItem.List_Price__c) * Number(qItem.Quantity__c);
									selectedTotalQty += Number(qItem.Quantity__c);
									var currentTimes = Number(qItem.Quantity__c) / Number(miniQty);
									if (times == -1) {
										times = currentTimes;
									} else if (times > currentTimes) {
										times = currentTimes;
									}
								}
							});
						}
					});
					if (selectedDiffModels < miniDiffModels) {
						times = 0;
					} else if (miniTotalAmount > 0) {
						if (miniTotalAmount > selectedTotalAmount) {
							times = 0;
						} else {
							var totalAmountTimes = selectedTotalAmount / miniTotalAmount;
							if (times > totalAmountTimes) {
								times = totalAmountTimes;
							}
						}
					} else if (miniTotalQty > 0) {
						if (miniTotalQty > selectedTotalQty) {
							times = 0;
						} else {
							var totalQtyTimes = selectedTotalQty / miniTotalQty;
							if (times > totalQtyTimes) {
								times = totalQtyTimes;
							}
						}
					}
				}
			});
			if (times != 0) {
				times = Math.floor(times);
			}
		} else if (promotion.promotion.Promotion_Type__c == "Price Break") {
			//have more than one rule
			var availableRule = this.getAvailableRuleInPriceBreak(quotationItemList, index, promotion);
			if (availableRule) {
				times = 1;
			}
		} else if (promotion.promotion.Promotion_Type__c == "Full Pallet Promo") {
			//only have one rule
			var thresholdList = promotion.ruleList[0].thresholdList;
			var hasFreeGoods = this.hasFreeGoods(promotion.ruleList[0]);
			if (hasFreeGoods) {
				thresholdList.forEach(function (tItem) {
					if (tItem.threshold.RecordType.DeveloperName == "By_Full_Pallet_Quantity_of_Specific_Product" && tItem.products[0].Product__r.ProductCode == currentProductCode) {
						var minimumQty = tItem.products[0].Minimum_Quantity__c * tItem.products[0].Product__r.Full_Pallet_Quantity__c;
						times = Math.floor(currentQty / minimumQty);
					}
				});
			} else {
				thresholdList.forEach(function (tItem) {
					if (tItem.threshold.RecordType.DeveloperName == "By_Full_Pallet_Quantity_of_Specific_Product") {
						var minimumQty = tItem.products[0].Minimum_Quantity__c * tItem.products[0].Product__r.Full_Pallet_Quantity__c;
						//times = Math.floor(currentQty/minimumQty);
						var productCode = tItem.products[0].Product__r.ProductCode;
						quotationItemList.forEach(function (qItem) {
							if (qItem.ProductCode__c == productCode && qItem.Promotion_Rule_Name__c == currentRuleName && (qItem.isThreshold || qItem.HasPromo)) {
								var qty = Number(qItem.Quantity__c);
								var currentTimes = Math.floor(qty / minimumQty);
								if (times == -1) {
									times = currentTimes;
								} else if (times > currentTimes) {
									times = currentTimes;
								}
							}
						});
					}
				});
			}
		} else if (promotion.promotion.Promotion_Type__c == "Others") {
			//have more than one rule
			var thresholdList = promotion.ruleList[0].thresholdList;
			thresholdList.forEach(function (tItem) {
				if (tItem.threshold.RecordType.DeveloperName == "By_Quantity_of_Specific_Product") {
					quotationItemList.forEach(function (qItem) {
						if (qItem.ProductCode__c == tItem.products[0].Product__r.ProductCode && qItem.HasPromo && qItem.Is_Initial__c) {
							//var minimumQty = tItem.products[0].Minimum_Quantity__c;
							var minimumQty = tItem.products[0].Increment_For_Free_Goods__c ? tItem.products[0].Increment_For_Free_Goods__c : tItem.products[0].Minimum_Quantity__c;
							if (times == -1) {
								times = qItem.Quantity__c / minimumQty;
							} else if (times > qItem.Quantity__c / minimumQty) {
								times = qItem.Quantity__c / minimumQty;
							}
						}
					});
				} else if (tItem.threshold.RecordType.DeveloperName == "By_Amount_of_Specific_Product") {
					quotationItemList.forEach(function (qItem) {
						if (qItem.ProductCode__c == tItem.products[0].Product__r.ProductCode && qItem.HasPromo && qItem.Is_Initial__c) {
							var minimumAmt = tItem.products[0].Minimum_Amount__c;
							var qAmt = qItem.List_Price__c * qItem.Quantity__c;
							if (times == -1) {
								times = qAmt / minimumAmt;
							} else if (times > qAmt / minimumAmt) {
								times = qAmt / minimumAmt;
							}
						}
					});
				} else if (tItem.threshold.RecordType.DeveloperName == "By_Full_Pallet_Quantity_of_Specific_Product") {
					quotationItemList.forEach(function (qItem) {
						if (qItem.ProductCode__c == tItem.products[0].Product__r.ProductCode && qItem.HasPromo && qItem.Is_Initial__c) {
							var minimumQty = tItem.products[0].Minimum_Quantity__c * tItem.products[0].Product__r.Full_Pallet_Quantity__c;
							if (times == -1) {
								times = qItem.Quantity__c / minimumQty;
							} else if (times > qItem.Quantity__c / minimumQty) {
								times = qItem.Quantity__c / minimumQty;
							}
						}
					});
				} else if (tItem.threshold.RecordType.DeveloperName == "By_Mix_Match") {
					var products = tItem.products;
					var miniTotalQty = tItem.threshold.Minimum_Total_Quantity__c;
					var miniTotalAmount = tItem.threshold.Minimum_Total_Amount__c;
					var miniDiffModels = tItem.threshold.Minimum_Different_Tool_Models__c;
					var selectedDiffModels = 0;
					var selectedTotalQty = 0;
					var selectedTotalAmount = 0.0;
					products.forEach(function (pItem) {
						var productCode = pItem.Product__r.ProductCode;
						var miniQty = pItem.Number__c;
						if (pItem.selected) {
							selectedDiffModels++;
							quotationItemList.forEach(function (qItem) {
								if (qItem.ProductCode__c == productCode && qItem.Promotion_Rule_Name__c == currentRuleName && (qItem.isThreshold || qItem.HasPromo)) {
									selectedTotalAmount += Number(qItem.List_Price__c) * Number(qItem.Quantity__c);
									selectedTotalQty += Number(qItem.Quantity__c);
									var currentTimes = Number(qItem.Quantity__c) / Number(miniQty);
									if (times == -1) {
										times = currentTimes;
									} else if (times > currentTimes) {
										times = currentTimes;
									}
								}
							});
						}
					});
					if (selectedDiffModels < miniDiffModels) {
						times = 0;
					} else if (miniTotalAmount > 0) {
						if (miniTotalAmount > selectedTotalAmount) {
							times = 0;
						} else {
							var totalAmountTimes = selectedTotalAmount / miniTotalAmount;
							if (times > totalAmountTimes) {
								times = totalAmountTimes;
							}
						}
					} else if (miniTotalQty > 0) {
						if (miniTotalQty > selectedTotalQty) {
							times = 0;
						} else {
							var totalQtyTimes = selectedTotalQty / miniTotalQty;
							if (times > totalQtyTimes) {
								times = totalQtyTimes;
							}
						}
					}
				}
			});
			if (times != 0) {
				times = Math.floor(times);
			}
		}
		return times;
	},
	isDuplicatePromo: function (quotationItemList, selectedPromotion) {
		var isDuplicatePromo = false;
		quotationItemList.forEach(function (qItem) {
			selectedPromotion.ruleList.forEach(function (rItem) {
				if (qItem.Promotion_Rule_Name__c && qItem.Promotion_Rule_Name__c == rItem.ruleName) {
					isDuplicatePromo = true;
				}
			});
		});
		return isDuplicatePromo;
	},
	getAvailableRuleInPriceBreak: function (quotationItemList, index, selectedPromotion) {
		var availableRule = null;
		var mainThresholdIndex = 0;
		if (quotationItemList[index].HasPromo) {
			mainThresholdIndex = index;
		} else {
			var ruleName = quotationItemList[index].Promotion_Rule_Name__c;
			var i;
			for (i = 0; i < quotationItemList.length; i++) {
				if (quotationItemList[i].Promotion_Rule_Name__c == ruleName && quotationItemList[i].HasPromo) {
					mainThresholdIndex = i;
					break;
				}
			}
		}
		var currentQuotation = quotationItemList[mainThresholdIndex];
		var currentProductCode = currentQuotation.ProductCode__c;
		var currentQty = currentQuotation.Quantity__c;
		var currentAmt = currentQuotation.List_Price__c * currentQuotation.Quantity__c;
		var ruleList = selectedPromotion.ruleList;
		ruleList.forEach(function (rItem) {
			var thresholdList = rItem.thresholdList;
			thresholdList.forEach(function (tItem) {
				if (tItem.threshold.RecordType.DeveloperName == "By_Quantity_of_Specific_Product" && tItem.products[0].Product__r.ProductCode == currentProductCode) {
					var minimumQty = tItem.products[0].Minimum_Quantity__c;
					var maximumQty = tItem.products[0].Maximum_Quantity__c;
					if (currentQty >= minimumQty && currentQty < maximumQty) {
						availableRule = rItem;
					}
				} else if (tItem.threshold.RecordType.DeveloperName == "By_Amount_of_Specific_Product" && tItem.products[0].Product__r.ProductCode == currentProductCode) {
					var minimumAmt = tItem.products[0].Minimum_Amount__c;
					var maximumQty = tItem.products[0].Maximum_Amount__c;
					if (currentAmt >= minimumAmt && currentAmt < maximumQty) {
						availableRule = rItem;
					}
				}
			});
		});
		return availableRule;
	},
	getWholeOrderPromotionThresholdTimes: function (quotationItemList, wholeOrderPromo) {
		var totalAmt = 0.0;
		var totalQuantity = 0;
		quotationItemList.forEach(function (qItem) {
			if (!qItem.isOffering && qItem.Product__c) {
				var qAmount = Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Promo_Discount_Amount__c) + Number(qItem.Discount_Amount__c);

				//var qAmount = Number(qItem.List_Price__c * qItem.Quantity__c);
				totalAmt += qAmount;
				totalQuantity += Number(qItem.Quantity__c);
			}
		});

		var threshold = wholeOrderPromo.ruleList[0].thresholdList[0].threshold;
		var times = 1;
		if (threshold.RecordType.DeveloperName == "By_Quantity_of_Whole_Order") {
			times = Math.floor(totalQuantity / threshold.Minimum_Whole_Order_Quantity__c);
		} else if (threshold.RecordType.DeveloperName == "By_Amount_of_Whole_Order") {
			times = Math.floor(totalAmt / threshold.Minimum_Whole_Order_Amount__c);
		}
		return times;
	},
	checkWholeOrderPromotion: function (quotationItemList, wholeOrderPromo, component) {
		if (wholeOrderPromo) {
			var isMeetWholeOrderPromo = this.isMeetWholeOrderPromo(quotationItemList, wholeOrderPromo);
			if (isMeetWholeOrderPromo) {
				this.removeWholeOrderPromotionOfferingItem(quotationItemList, wholeOrderPromo, component);
				this.calculateWithWholeOrderPromo(quotationItemList, wholeOrderPromo, component);
				this.checkMeetWholeOrderPromoOfferingPoolLimit(quotationItemList, component);
			} else {
				this.removeWholeOrderPromotionOfferingItem(quotationItemList, wholeOrderPromo, component);
				component.set("v.wholeOrderPromo", "");
			}
		}
	},
	checkPaymentTermPromotion: function (quotationItemList, termsPromo, component) {
		if (termsPromo) {
			var isMeetPaymentTermPromo = this.isMeetPaymentTermPromo(quotationItemList, termsPromo);
			if (!isMeetPaymentTermPromo) {
				component.set("v.termsPromo", "");
				this.getPaymentTermRule(component);
			}
		}
	},
	checkMeetOfferingPoolLimit: function (quotationItemList, index, promotion) {
		//var currentProductCode = quotationItemList[index].ProductCode__c;
		var offeringList = promotion.ruleList[0].offeringList;
		var ruleName = quotationItemList[index].Promotion_Rule_Name__c;
		var mainThresholdIndex = 0;
		var i;
		for (i = 0; i < quotationItemList.length; i++) {
			if (quotationItemList[i].Promotion_Rule_Name__c == ruleName && quotationItemList[i].HasPromo) {
				mainThresholdIndex = i;
				break;
			}
		}
		var times = this.getThresholdTimes(quotationItemList, mainThresholdIndex, promotion);
		if (promotion.promotion.Promotion_Type__c == "Price Break") {
			var availableRule = this.getAvailableRuleInPriceBreak(quotationItemList, mainThresholdIndex, promotion);
			if (availableRule) {
				offeringList = availableRule.offeringList;
			}
		}

		var j;
		for (j = 0; j < offeringList.length; j++) {
			var oItem = offeringList[j];
			if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
				var maxGiftQty = oItem.offering.Gift_Total_Quantity__c * times;
				var totalGiftQty = 0;
				var products = oItem.products;
				var giftQuotationList = [];
				var isExceed = false;
				products.forEach(function (pItem) {
					if (pItem.selected) {
						quotationItemList.forEach(function (qItem) {
							if (pItem.Product__r.ProductCode == qItem.ProductCode__c && qItem.Promotion_Rule_Name__c == ruleName && qItem.isOffering) {
								totalGiftQty += Number(qItem.Quantity__c);
								giftQuotationList.push(qItem);
								if (qItem.Quantity__c > pItem.Number__c * times) {
									isExceed = true;
									qItem.isExceed = true;
									qItem.isLess = false;
								} else {
									qItem.isExceed = false;
									if (qItem.Quantity__c < pItem.Number__c * times) {
										qItem.isLess = true;
									} else {
										qItem.isLess = false;
									}
								}
							}
						});
					}
				});
				if (totalGiftQty > maxGiftQty) {
					isExceed = true;
					giftQuotationList.forEach(function (gItem) {
						gItem.isExceed = true;
						gItem.isLess = false;
					});
				}

				if (totalGiftQty < maxGiftQty) {
					giftQuotationList.forEach(function (gItem) {
						if (gItem.isExceed) {
							gItem.isLess = false;
						}
					});
				}

				if (totalGiftQty == maxGiftQty) {
					giftQuotationList.forEach(function (gItem) {
						gItem.isLess = false;
					});
				}
			}
		}
	},
	isAllMeetThreshold: function (component) {
		var isAllMeetThreshold = true;
		var quotationItemList = component.get("v.quotationItemList");
		quotationItemList.forEach(function (qItem) {
			if (qItem.Promotion && (qItem.HasPromo || qItem.isThreshold) && qItem.isMix && !qItem.isMeet) {
				isAllMeetThreshold = false;
			} else if (qItem.Promotion && qItem.isOffering && qItem.isPool && qItem.isExceed) {
				isAllMeetThreshold = false;
			}
		});
		return isAllMeetThreshold;
	},
	findInitialRuleInPriceBreak: function (orderItem, selectedPromotion) {
		var availableRule = null;
		var currentQuotation = orderItem;
		var currentProductCode = currentQuotation.ProductCode__c;
		var currentQty = currentQuotation.Quantity__c;
		var currentAmt = currentQuotation.List_Price__c * currentQuotation.Quantity__c;
		var ruleList = selectedPromotion.ruleList;
		ruleList.forEach(function (rItem) {
			var thresholdList = rItem.thresholdList;
			thresholdList.forEach(function (tItem) {
				if (tItem.threshold.RecordType.DeveloperName == "By_Quantity_of_Specific_Product" && tItem.products[0].Product__r.ProductCode == currentProductCode) {
					var minimumQty = tItem.products[0].Minimum_Quantity__c;
					var maximumQty = tItem.products[0].Maximum_Quantity__c;
					if (currentQty >= minimumQty && currentQty < maximumQty) {
						availableRule = rItem;
					}
				} else if (tItem.threshold.RecordType.DeveloperName == "By_Amount_of_Specific_Product" && tItem.products[0].Product__r.ProductCode == currentProductCode) {
					var minimumAmt = tItem.products[0].Minimum_Amount__c;
					var maximumQty = tItem.products[0].Maximum_Amount__c;
					if (currentAmt >= minimumAmt && currentAmt < maximumQty) {
						availableRule = rItem;
					}
				}
			});
		});
		return availableRule;
	},
	initialAvailablePromotion: function (component) {
		var orderItemList = component.get("v.quotationItemList");
		var customerId = component.get("v.customerId");
		var wholeOrderPromoCode = "";
		var initialProductSet = new Set();
		orderItemList.forEach(function (oItem) {
			if (oItem.Is_Initial__c) {
				var promotionList = oItem.promotionList;
				if (!promotionList) {
					initialProductSet.add(oItem.Product__c);
				}
			} else if (oItem.PromotionName__c) {
				if (oItem.Unit_Price__c > 0) {
					oItem.isThreshold = true;
				} else {
					oItem.isOffering = true;
				}
			}
		});
		component.set("v.quotationItemList", orderItemList);

		initialProductSet.forEach(function (pId) {
			var action = component.get("c.getPriceBook");
			action.setParams({
				"prodId": pId,
				"customerId": component.get("v.customerId"),
				"orderType": component.get("v.orderTypeVal")
			});
			action.setCallback(this, function (response) {
				var state = response.getState();
				if (state === "SUCCESS") {
					var result = response.getReturnValue();
					if (result) {
						var data = JSON.parse(result);
						if (data) {
							var promotionList = data.promotionList;
							var productCode = data.product.ProductCode;
							var orderList = component.get("v.quotationItemList");
							orderList.forEach(function (oItem) {
								//YANKO
								var orgcode = component.get("v.OrgCode");
								if (oItem.Product__r.CS_Exchange_Rate__c && orgcode != "CCA") {
									oItem.CS_Exchange_Rate__c = oItem.Product__r.CS_Exchange_Rate__c;
								} else {
									oItem.CS_Exchange_Rate__c = 1;
								}
								if (oItem.Is_Initial__c && oItem.ProductCode__c == productCode) {
									oItem.promotionList = promotionList;
									if (promotionList && promotionList.length > 0) {
										oItem.HasPromo = true;
										var promotionCode = oItem.PromotionName__c;
										var promotionObj;
										for (var i = 0; i < promotionList.length; i++) {
											if (promotionList[i].promotion.Promotion_Code_For_External__c == promotionCode) {
												promotionObj = promotionList[i];
												break;
											}
										}
										if (promotionObj) {
											oItem.Promotion = promotionObj;
											var ruleItem = promotionObj.ruleList[0];
											if (promotionObj.promotion.Promotion_Type__c == "Price Break") {
												var availableRule = null;
												var currentQuotation = oItem;
												var currentProductCode = currentQuotation.ProductCode__c;
												var currentQty = currentQuotation.Quantity__c;
												var currentAmt = currentQuotation.List_Price__c * currentQuotation.Quantity__c;
												promotionObj.ruleList.forEach(function (rItem) {
													var thresholdList = rItem.thresholdList;
													thresholdList.forEach(function (tItem) {
														if (
															tItem.threshold.RecordType.DeveloperName == "By_Quantity_of_Specific_Product" &&
															tItem.products[0].Product__r.ProductCode == currentProductCode
														) {
															var minimumQty = tItem.products[0].Minimum_Quantity__c;
															var maximumQty = tItem.products[0].Maximum_Quantity__c;
															if (currentQty >= minimumQty && currentQty < maximumQty) {
																availableRule = rItem;
															}
														} else if (
															tItem.threshold.RecordType.DeveloperName == "By_Amount_of_Specific_Product" &&
															tItem.products[0].Product__r.ProductCode == currentProductCode
														) {
															var minimumAmt = tItem.products[0].Minimum_Amount__c;
															var maximumQty = tItem.products[0].Maximum_Amount__c;
															if (currentAmt >= minimumAmt && currentAmt < maximumQty) {
																availableRule = rItem;
															}
														}
													});
												});
												ruleItem = availableRule;
											}

											var selectedThresholdProducts = [];
											var selectedOfferingProducts = [];
											for (var j = 0; j < orderList.length; j++) {
												if (orderList[j].PromotionName__c == promotionCode && !orderList[j].Is_Initial__c) {
													orderList[j].Promotion = promotionObj;
													if (orderList[j].Unit_Price__c > 0) {
														selectedThresholdProducts.push(orderList[j]);
													} else {
														selectedOfferingProducts.push(orderList[j]);
													}
												}
											}
											ruleItem.thresholdList.forEach(function (thresholdItem) {
												if (thresholdItem.threshold.RecordType.DeveloperName == "By_Mix_Match") {
													var products = thresholdItem.products;
													for (var i = 0; i < products.length; i++) {
														if (products[i].Product__r.ProductCode == productCode) {
															products[i].selected = true;
														}

														selectedThresholdProducts.forEach(function (selectedItem) {
															if (selectedItem.ProductCode__c == products[i].Product__r.ProductCode) {
																selectedItem.isMix = true;
																selectedItem.isMeet = true;
																oItem.isMix = true;
																oItem.hasMix = true;
																oItem.isMeet = true;
																products[i].selected = true;
															}
														});
													}
												}
											});
											ruleItem.offeringList.forEach(function (offeringItem) {
												if (offeringItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
													var products = offeringItem.products;
													for (var i = 0; i < products.length; i++) {
														selectedOfferingProducts.forEach(function (selectedItem) {
															if (selectedItem.ProductCode__c == products[i].Product__r.ProductCode) {
																selectedItem.isPool = true;
																selectedItem.isExceed = false;
																oItem.hasPool = true;
																products[i].selected = true;
															}
														});
													}
												}
											});
										}

										var promotionCodesStr = "";
										data.promotionList.forEach(function (pItem) {
											promotionCodesStr += "'" + pItem.promotion.Promo_Code__c + "',";
										});
										oItem.strCodes = promotionCodesStr.slice(0, -1);
										// oItem.promotionFilterCondition = '[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Open"},{"FieldName":"Promo_Code__c", "Condtion":"IN", "Value":"('+ strCodes + ')"}]';
									} else {
										oItem.HasPromo = false;
										oItem.strCodes = "onfous";
										// oItem.promotionFilterCondition = '[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Error"}]';
									}
								}
							});
						}
					}
				} else {
					var errors = response.getError();
					if (errors) {
						if (errors[0] && errors[0].message) {
							component.set("v.isBusy", false);
							console.log("ERROR: " + errors[0].message);
						}
					} else {
						component.set("v.isBusy", false);
						console.log("ERROR: Unknown error");
					}
				}
				component.set("v.quotationItemList", orderList);
			});
			$A.enqueueAction(action);
		});
	},
	initialWholeOrderPromotion: function (component) {
		var orderItemList = component.get("v.quotationItemList");
		if (orderItemList == null || orderItemList.length == 0) {
			return;
		}
		var wholeOrderPromoCode = orderItemList[0].Whole_Order_Promotion_Code__c;
		var wholeOrderPromotion = component.get("v.wholeOrderPromo");
		if (wholeOrderPromotion) {
			return;
		}
		if (wholeOrderPromoCode) {
			var action = component.get("c.getPromotion");
			action.setParams({
				"promotionCode": wholeOrderPromoCode,
				"customerId": component.get("v.customerId"),
				"isDropShip": component.get("v.isDropShip")
			});
			action.setCallback(this, function (response) {
				var state = response.getState();
				if (state === "SUCCESS") {
					var result = response.getReturnValue();
					if (result) {
						var data = JSON.parse(result);
						if (data) {
							var promoObj = data.promotion;
							if (promoObj) {
								component.set("v.wholeOrderPromo", data);
							} else {
								var wholeOrderPromo = {
									"promotion": {
										"Promotion_Code_For_External__c": wholeOrderPromoCode
									}
								};
								this.removeWholeOrderPromotionOfferingItem(orderItemList, wholeOrderPromo, component);
								component.set("v.quotationItemList", []);
								component.set("v.quotationItemList", orderItemList);
								this.calculateTotal(component);
							}
						}
					} else {
						var toastEvent = $A.get("e.force:showToast");
						toastEvent.setParams({
							"title": "Warning!",
							"message": `The [${wholeOrderPromoCode}] is invalid. Please re-enter this order.`,
							"type": "Warning",
                            "mode": "sticky"
                        });
                        toastEvent.fire();
                        component.set("v.quotationItemList", []);
						//alert("ERROR: Invaild Promotion.");
					}
				} else {
					var errors = response.getError();
					if (errors) {
						if (errors[0] && errors[0].message) {
							alert("ERROR: " + errors[0].message);
						}
					} else {
						alert("ERROR: Unknown error");
					}
				}
			});
			$A.enqueueAction(action);
		}
	},
	initialPaymentTermPromotion: function (component) {
		var quotation = component.get("v.quotation");
		var paymentTermCode = quotation.Payment_Term_Promotion_Code__c;
		var termsPromotion = component.get("v.termsPromo");
		// if (termsPromotion) {
		// 	return;
		// }
		if (paymentTermCode) {
			var action = component.get("c.getPromotion");
			action.setParams({
				"promotionCode": paymentTermCode,
				"customerId": component.get("v.customerId"),
				"isDropShip": component.get("v.isDropShip")
			});
			action.setCallback(this, function (response) {
				var state = response.getState();
				if (state === "SUCCESS") {
					var result = response.getReturnValue();
					if (result) {
						var data = JSON.parse(result);
						if (data) {
							var promoObj = data.promotion;
							if (promoObj) {
								component.set("v.termsPromo", data);
								var termKey = data.ruleList[0].offeringList[0].offering.Payment_Term__c;
								var termLabel = data.ruleList[0].offeringList[0].offering.Payment_Term_Label__c;
								component.set("v.paymentTermLabel", termLabel.substring(termKey.length + 3));
								component.set("v.paymentTermValue", termKey);
                                component.set("v.termsPromoPTValue", termKey);
                                if (promoObj.Promotion_Type__c == "Payment Term Promo") {
                                    component.set("v.needResearch", false);
                                }
							}
						}
					} else {
						// var toastEvent = $A.get("e.force:showToast");
						// toastEvent.setParams({
						// 	"title": "Warning!",
						// 	"message": "Invaild Promotion.",
						// 	"type": "Warning"
						// });
                        // toastEvent.fire();
                        var toastEvent = $A.get("e.force:showToast");
						toastEvent.setParams({
							"title": "Warning!",
							"message": `The [${paymentTermCode}] is invalid. Please re-enter this order.`,
							"type": "Warning",
                            "mode": "sticky"
                        });
                        toastEvent.fire();
                        component.set("v.quotationItemList", []);
						//alert("ERROR: Invaild Promotion.");
					}
				} else {
					var errors = response.getError();
					if (errors) {
						if (errors[0] && errors[0].message) {
							alert("ERROR: " + errors[0].message);
						}
					} else {
						alert("ERROR: Unknown error");
					}
				}
			});
			$A.enqueueAction(action);
		}
	},
	getPaymentTermRule: function (component) {
		var action = component.get("c.getPaymentTerm");
		action.setParam("customerId", component.get("v.customerId"));
		action.setParam("brandName", component.get("v.brandScope"));
		action.setParam("recordId", component.get("v.recordId"));
		action.setParam("total", component.get("v.quotation.Product_Price__c"));
		action.setCallback(this, function (response) {
			var state = response.getState();
			if (state === "SUCCESS") {
				var result = JSON.parse(response.getReturnValue());
				if (result) {
					if (!component.get("v.termsPromo")) {
						var brand = component.get("v.brandScope");
						var customerCluster = component.get("v.customerCluster");
						var defaultPaymentTerm = component.get("v.defaultPaymentTerm");
						var customerOrgCode = component.get("v.customerOrgCode");
						let NAScope =
							brand == "EGO" &&
							(customerCluster == "CNA-CG11" || customerCluster == "CNA-CG10" || customerCluster == "CNA-CG20") &&
							defaultPaymentTerm == "NA001" &&
							customerOrgCode != "CCA";
						let CAScope = brand == "EGO" && customerOrgCode == "CCA" && (customerCluster == "CA-CG11" || customerCluster == "CA-CG12" || customerCluster == "CA-CG05");
						if (NAScope || CAScope) {
							this.rebindPaymentHandler(component, null, this, false);
						} else {
							component.set("v.paymentTermLabel", result.paymentTermLabel);
							component.set("v.paymentTermValue", result.paymentTerm);
							this.rebindPaymentHandler(component, null, this, false);
						}
					}
				}
			} else {
				console.log(response.getError());
			}
		});
		$A.enqueueAction(action);
	},
	clearWholeOrderPromotion: function (component) {
		component.set("v.wholeOrderPromo", "");
		component.set("v.termsPromo", "");
	},
	initReminderPromoList: function (quotationItemList) {
		var reminderPromoList = [];
		var index = 1;
		quotationItemList.forEach(function (qItem) {
			if (qItem.HasPromo && qItem.Promotion) {
				var reminderItem = {};
				reminderItem.name = "Order Line " + index;
				reminderItem.thresholdDesc = "Buy ";
				reminderItem.offeringDesc = " you can get ";

				var selectedPromotion = qItem.Promotion;
				var ruleName = qItem.Promotion_Rule_Name__c;
				var thresholdList = selectedPromotion.ruleList[0].thresholdList;
				var offeringList = selectedPromotion.ruleList[0].offeringList;
				//var quantity = qItem.Quantity__c;
				//var prise = qItem.List_Price__c;
				if (selectedPromotion.promotion.Promotion_Type__c == "BOGO") {
					thresholdList.forEach(function (tItem) {
						tItem.products.forEach(function (pItem) {
							var minimumQty = pItem.Increment_For_Free_Goods__c ? pItem.Increment_For_Free_Goods__c : pItem.Minimum_Quantity__c;
							quotationItemList.forEach(function (quoItem) {
								if (quoItem.Promotion_Rule_Name__c == ruleName && (quoItem.HasPromo || quoItem.isThreshold) && quoItem.ProductCode__c == pItem.Product__r.ProductCode) {
									var quantity = quoItem.Quantity__c;
									var prise = quoItem.List_Price__c;
									var time = Math.floor(quantity / minimumQty);
									var offset = (time + 1) * minimumQty - quantity;
									reminderItem.thresholdDesc += offset + " more " + pItem.Product__r.ProductCode + ",";
								}
							});
						});
					});
				} else if (selectedPromotion.promotion.Promotion_Type__c == "Mix & Match") {
					reminderItem.thresholdDesc = "";
				} else if (selectedPromotion.promotion.Promotion_Type__c == "Price Discount") {
					thresholdList.forEach(function (tItem) {
						if (tItem.threshold.RecordType.DeveloperName == "By_Quantity_of_Specific_Product") {
							tItem.products.forEach(function (pItem) {
								var minimumQty = pItem.Minimum_Quantity__c;
								quotationItemList.forEach(function (quoItem) {
									if (quoItem.Promotion_Rule_Name__c == ruleName && (quoItem.HasPromo || quoItem.isThreshold) && quoItem.ProductCode__c == pItem.Product__r.ProductCode) {
										var quantity = quoItem.Quantity__c;
										var prise = quoItem.List_Price__c;
										var time = Math.floor(quantity / minimumQty);
										var offset = (time + 1) * minimumQty - quantity;
										reminderItem.thresholdDesc += offset + " more " + pItem.Product__r.ProductCode + ",";
									}
								});
							});
						} else if (tItem.threshold.RecordType.DeveloperName == "By_Amount_of_Specific_Product") {
							tItem.products.forEach(function (pItem) {
								var minimumAmt = pItem.Minimum_Amount__c;
								quotationItemList.forEach(function (quoItem) {
									if (quoItem.Promotion_Rule_Name__c == ruleName && (quoItem.HasPromo || quoItem.isThreshold) && quoItem.ProductCode__c == pItem.Product__r.ProductCode) {
										var quantity = quoItem.Quantity__c;
										var prise = quoItem.List_Price__c;
										var time = Math.floor((prise * quantity) / minimumAmt);
										var qtyNext = Math.floor(((time + 1) * minimumAmt) / prise);
										if (qtyNext * prise < (time + 1) * minimumAmt) {
											qtyNext++;
										}
										var offset = qtyNext - quantity;
										reminderItem.thresholdDesc += offset + " more " + pItem.Product__r.ProductCode + ",";
									}
								});
							});
						}
					});
				} else if (selectedPromotion.promotion.Promotion_Type__c == "Full Pallet Promo") {
					thresholdList.forEach(function (tItem) {
						tItem.products.forEach(function (pItem) {
							var minimumQty = pItem.Minimum_Quantity__c * pItem.Product__r.Full_Pallet_Quantity__c;
							quotationItemList.forEach(function (quoItem) {
								if (quoItem.Promotion_Rule_Name__c == ruleName && (quoItem.HasPromo || quoItem.isThreshold) && quoItem.ProductCode__c == pItem.Product__r.ProductCode) {
									var quantity = quoItem.Quantity__c;
									var prise = quoItem.List_Price__c;
									var time = Math.floor(quantity / minimumQty);
									var offset = (time + 1) * minimumQty - quantity;
									reminderItem.thresholdDesc += offset + " more " + pItem.Product__r.ProductCode + ",";
								}
							});
						});
					});
				} else if (selectedPromotion.promotion.Promotion_Type__c == "Price Break") {
					reminderItem.thresholdDesc = "";
				} else if (selectedPromotion.promotion.Promotion_Type__c == "Others") {
					reminderItem.thresholdDesc = "";
				}

				offeringList.forEach(function (oItem) {
					if (oItem.offering.RecordType.DeveloperName == "Specific_Free_Good") {
						oItem.products.forEach(function (pItem) {
							reminderItem.offeringDesc += pItem.Gift_Quantity__c + " more " + pItem.Product__r.ProductCode + ",";
						});
						reminderItem.offeringDesc = reminderItem.offeringDesc.slice(0, -1) + " for free. ";
					} else if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
						// oItem.products.forEach(function(pItem){
						//     reminderItem.offeringDesc+= pItem.Number__c  + ' more ' + pItem.Product__r.ProductCode + ',';
						// });
						reminderItem.offeringDesc += oItem.offering.Gift_Total_Quantity__c + " more products,";
						reminderItem.offeringDesc = reminderItem.offeringDesc.slice(0, -1) + " for free. ";
					} else if (oItem.offering.RecordType.DeveloperName == "Amount_Off") {
						reminderItem.offeringDesc = "";
					} else if (oItem.offering.RecordType.DeveloperName == "Discount_Off") {
						reminderItem.offeringDesc = "";
					}
				});

				if (reminderItem.offeringDesc && reminderItem.thresholdDesc) {
					reminderPromoList.push(reminderItem);
				}
			}
			index++;
		});
		return reminderPromoList;
	},
	checkMixMatchOrder: function (component) {
		var quotationItemList = component.get("v.quotationItemList");
		for (var i = 0; i < quotationItemList.length; i++) {
			var currentQuotation = quotationItemList[i];
			if (currentQuotation.Is_Initial__c) {
				var promotion = currentQuotation.Promotion;
				if (promotion && this.hasMixMatch(promotion)) {
					var ruleName = currentQuotation.Promotion_Rule_Name__c;
					var isMeetThreshold = this.isMeetThreshold(quotationItemList, i, promotion);
					if (!isMeetThreshold) {
						quotationItemList.forEach(function (qItem) {
							if (qItem.Promotion_Rule_Name__c == ruleName && qItem.isMix) {
								qItem.isMeet = false;
							}
						});
					}
				}

				if (promotion) {
					var ruleItem = promotion.ruleList[0];
					if (promotion.promotion.Promotion_Type__c == "Price Break") {
						var availableRuleItem = this.getAvailableRuleInPriceBreak(quotationItemList, i, promotion);
						if (availableRuleItem) {
							ruleItem = availableRuleItem;
						}
					}

					if (promotion && this.isPoolFreeGoods(ruleItem)) {
						this.checkMeetOfferingPoolLimit(quotationItemList, i, promotion);
					}
				}
			}
		}
		component.set("v.quotationItemList", quotationItemList);
	},
	checkOrderQty: function (component) {
		var quotationItemList = component.get("v.quotationItemList");
		if (quotationItemList && quotationItemList.length > 0) {
			var hasEmptyQty = false;
			var hasLanuchDateGreaterShipDate = false;
			quotationItemList.forEach(function (qItem) {
				if (!qItem.Quantity__c || qItem.Quantity__c == 0) {
					hasEmptyQty = true;
				}
			});
			// quotationItemList.forEach(function (qItem) {
			//     if (qItem.Ship_Date__c < qItem.Lanch_Date__c) {
			//         hasLanuchDateGreaterShipDate = true;
			//     }
			// });
			// if(hasLanuchDateGreaterShipDate){
			//     return false;
			// }
			if (hasEmptyQty) {
				var toastEvent = $A.get("e.force:showToast");
				toastEvent.setParams({
					"title": "Warning!",
					"message": "Qty must be larger than 0.",
					"type": "Warning"
				});
				toastEvent.fire();
				return false;
			}

			quotationItemList.forEach(function (qItem) {
				if (!qItem.Promotion__c) {
					qItem.PromotionName__c = "";
				}
			});
			component.set("v.quotationItemList", quotationItemList);

			//check Mix&Match Order
			this.checkMixMatchOrder(component);

			if (!this.isAllMeetThreshold(component)) {
				var toastEvent = $A.get("e.force:showToast");
				toastEvent.setParams({
					"title": "Warning!",
					"message": "There are order items that do not meet its promotion rules.",
					"type": "Warning"
				});
				toastEvent.fire();
				return false;
			}
		}
		return true;
	},
	hasMixMatch: function (selectedPromotion) {
		var thresholdList = selectedPromotion.ruleList[0].thresholdList;
		var hasMixMatch = false;
		thresholdList.forEach(function (tItem) {
			if (tItem.threshold.RecordType.DeveloperName == "By_Mix_Match") {
				hasMixMatch = true;
			}
		});
		return hasMixMatch;
	},
	checkMeetWholeOrderPromoOfferingPoolLimit: function (quotationItemList, component) {
		var wholeOrderPromo = component.get("v.wholeOrderPromo");
		var times = this.getWholeOrderPromotionThresholdTimes(quotationItemList, wholeOrderPromo);
		var offeringList = wholeOrderPromo.ruleList[0].offeringList;
		offeringList.forEach(function (oItem) {
			if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
				var maxGiftQty = oItem.offering.Gift_Total_Quantity__c * times;
				var totalGiftQty = 0;
				var products = oItem.products;
				var giftQuotationList = [];
				products.forEach(function (pItem) {
					if (pItem.selected) {
						quotationItemList.forEach(function (qItem) {
							if (pItem.Product__r.ProductCode == qItem.ProductCode__c && qItem.Promotion__c == wholeOrderPromo.promotion.Id && qItem.isOffering) {
								totalGiftQty += Number(qItem.Quantity__c);
								giftQuotationList.push(qItem);
								if (qItem.Quantity__c > pItem.Number__c * times) {
									qItem.isExceed = true;
									qItem.isLess = false;
								} else {
									qItem.isExceed = false;
									if (qItem.Quantity__c < pItem.Number__c * times) {
										qItem.isLess = true;
									} else {
										qItem.isLess = false;
									}
								}
							}
						});
					}
				});
				if (totalGiftQty > maxGiftQty) {
					giftQuotationList.forEach(function (gItem) {
						gItem.isExceed = true;
						gItem.isLess = false;
					});
				}

				if (totalGiftQty < maxGiftQty) {
					giftQuotationList.forEach(function (gItem) {
						if (gItem.isExceed) {
							gItem.isLess = false;
						}
					});
				}

				if (totalGiftQty == maxGiftQty) {
					giftQuotationList.forEach(function (gItem) {
						gItem.isLess = false;
					});
				}
			}
		});
	},
	hasFreeGoods: function (ruleItem) {
		var offeringList = ruleItem.offeringList;
		var hasFreeGoods = false;
		offeringList.forEach(function (oItem) {
			if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
				hasFreeGoods = true;
			}
			if (oItem.offering.RecordType.DeveloperName == "Specific_Free_Good") {
				hasFreeGoods = true;
			}
		});
		return hasFreeGoods;
	},
	getInitailIndex: function (quotationItemList, ruleName) {
		var index = 0;
		for (var i = 0; i < quotationItemList.length; i++) {
			var qItem = quotationItemList[i];
			if (qItem.Promotion_Rule_Name__c == ruleName && qItem.Is_Initial__c) {
				index = i;
				break;
			}
		}
		return index;
	},
	getMiniRuleInPriceBreak: function (selectedPromotion) {
		var miniRule = null;
		var tmpNum = -1;
		var ruleList = selectedPromotion.ruleList;
		ruleList.forEach(function (rItem) {
			var thresholdList = rItem.thresholdList;
			thresholdList.forEach(function (tItem) {
				if (tItem.threshold.RecordType.DeveloperName == "By_Quantity_of_Specific_Product") {
					var minimumQty = tItem.products[0].Minimum_Quantity__c;
					if (tmpNum == -1 || tmpNum > minimumQty) {
						tmpNum = minimumQty;
						miniRule = rItem;
					}
				} else if (tItem.threshold.RecordType.DeveloperName == "By_Amount_of_Specific_Product") {
					var minimumAmt = tItem.products[0].Minimum_Amount__c;
					if (tmpNum == -1 || tmpNum > minimumAmt) {
						tmpNum = minimumAmt;
						miniRule = rItem;
					}
				}
			});
		});
		return miniRule;
	},
	showToast: function (strTitle, strMessage, strType) {
		let title = strTitle || "",
			message = strMessage || "",
			type = strType || "Warning",
			event = $A.get("e.force:showToast");
		event.setParams({
			title,
			message,
			type
		});
		event.fire();
	},
	calculateNumMulti: function (num1, num2) {
		var baseNum = 0;
		try {
			baseNum += num1.toString().split(".")[1].length;
		} catch (e) {}
		try {
			baseNum += num2.toString().split(".")[1].length;
		} catch (e) {}

		return (Number(num1.toString().replace(".", "")) * Number(num2.toString().replace(".", ""))) / Math.pow(10, baseNum);
    },
    getCurrentLaunchDate: function (component) {
        var expectDate = component.get("v.quotation.Expected_Delivery_Date__c");
        var minSelectDate = component.get("v.minSelectDate");
        if (expectDate < minSelectDate) {
            component.set("v.showMessage", true);
            component.set("v.messageText", 'Value must be ' + minSelectDate + ' or later.');
        }
		var action = component.get("c.getCurrentLaunchDate");
		action.setParam("recordId", component.get("v.recordId"));
		action.setCallback(this, function (response) {
			var state = response.getState();
			if (state === "SUCCESS") {
				var result = JSON.parse(response.getReturnValue());
                if (result) {
                    var expectDate1 = component.get("v.quotation.Expected_Delivery_Date__c");
                    var quotationItemList = component.get("v.quotationItemList");
                    quotationItemList.forEach(function (qItem) {
                        var launchDate = result[qItem.ProductCode__c];
                        qItem.showMessage = false;
                        if (launchDate) {
                            qItem.Lanch_Date__c = launchDate;
                        }
                        if (expectDate < minSelectDate) {
                            qItem.minSelectDate = minSelectDate;
                        }
                        if (qItem.Lanch_Date__c == null || qItem.Lanch_Date__c == '' || qItem.Lanch_Date__c == undefined) {
                            qItem.shipDateGreaterLanuchDate = false;
                        }else if (qItem.Lanch_Date__c > qItem.Ship_Date__c) {
                            if (qItem.Lanch_Date__c < minSelectDate) {
                                qItem.shipDateGreaterLanuchDate = false;
                                qItem.minSelectDate = minSelectDate;
                            } else {
                                qItem.Ship_Date__c = qItem.Lanch_Date__c;
                                qItem.shipDateGreaterLanuchDate = true;
                                qItem.minSelectDate = qItem.Lanch_Date__c;
                            }
                        } else if (qItem.Lanch_Date__c == qItem.Ship_Date__c) {
                            if (qItem.Lanch_Date__c < minSelectDate) {
                                qItem.shipDateGreaterLanuchDate = false;
                                qItem.minSelectDate = minSelectDate;
                            } else {
                                qItem.Ship_Date__c = qItem.Lanch_Date__c;
                                qItem.shipDateGreaterLanuchDate = true;
                                qItem.minSelectDate = qItem.Lanch_Date__c;
                            }
                        } else {
                            if (qItem.Lanch_Date__c < minSelectDate) {
                                qItem.shipDateGreaterLanuchDate = false;
                                qItem.minSelectDate = minSelectDate;
                            } else {
                                qItem.shipDateGreaterLanuchDate = true;
                                qItem.minSelectDate = qItem.Lanch_Date__c;
                            }
                        }
                         if(qItem.Ship_Date__c < qItem.minSelectDate){
                             qItem.showMessage = true;
                             qItem.messageText = 'Value must be ' + qItem.minSelectDate + ' or later.';
                        }
                    });
                    component.set("v.quotationItemList", quotationItemList);
				}
			} else {
				console.log(response.getError());
			}
		});
		$A.enqueueAction(action);
    },
    checkPromotionValid: function (component, event, helper) {
        var quotationItemList = component.get("v.quotationItemList");
        //check promotions
		let promoCodes = new Set();
		quotationItemList.forEach(function (qItem) {
			if (qItem.PromotionName__c) {
				promoCodes.add(qItem.PromotionName__c);
			}
		});

		var termsPromo = component.get("v.termsPromo");
		if (termsPromo) {
			promoCodes.add(termsPromo.promotion.Promotion_Code_For_External__c);
		}

		var wholeOrderPromo = component.get("v.wholeOrderPromo");
		if (wholeOrderPromo) {
			promoCodes.add(wholeOrderPromo.promotion.Promotion_Code_For_External__c);
		}

		if (promoCodes.size > 0) {
			component.set("v.isBusy", true);
			var action = component.get("c.checkPromotions");
			action.setParams({
				"promotionCodes": Array.from(promoCodes),
				"customerId": component.get("v.customerId")
			});
			action.setCallback(this, function (response) {
				var state = response.getState();
				console.log("state--->" + state);
				if (state === "SUCCESS") {
					var results = response.getReturnValue();
					if (results != null && results != undefined) {
						var data = JSON.parse(results);
						if (data != null && data != undefined) {
							if (!data.isSuccess){
								var toastEvent = $A.get("e.force:showToast");
								toastEvent.setParams({
									"title": "Warning!",
									"message": data.errorMsg,
                                    "type": "Warning",
                                    "mode": "sticky"
								});
                                toastEvent.fire();
                                component.set("v.quotationItemList", []);
                                component.set("v.isBusy", false);

							}
						}
					}
				} else {
					var errors = response.getError();
					if (errors) {
						if (errors[0] && errors[0].message) {
							component.set("v.isBusy", false);
							alert("ERROR: " + errors[0].message);
						}
					} else {
						component.set("v.isBusy", false);
						alert("ERROR: Unknown error");
					}
				}
			});
			$A.enqueueAction(action);
		}
    },
    getCurrentPriceList:  function (component, event, helper) {
        component.set("v.isBusy", true);
        helper.delay(2000).then(function () {
            var isNotChange = true;
            component.set("v.isBusy", true);
			var action = component.get("c.getCurrentPriceList");
			action.setParam("recordId", component.get("v.recordId"));
			action.setParam("orderType", component.get("v.orderTypeVal"));
			action.setCallback(this, function (response) {
				var state = response.getState();
                if (state === "SUCCESS") {
                    component.set("v.isBusy", true);
					var result = JSON.parse(response.getReturnValue());
	                if (result) {
	                    var quotationItemList = component.get("v.quotationItemList");
	                    quotationItemList.forEach(function (qItem) {
	                        var price = result[qItem.ProductCode__c];
                            // qItem.List_Price__c = price;
                            var proDiscountAmount = 0;
                            var wholeProDiscountAmount = 0;
                            var allPromoDiscountAmount = 0;
                            if (qItem.Promo_Discount_Amount__c) {
                                proDiscountAmount = qItem.Promo_Discount_Amount__c * 100;
                            }
                            if (qItem.Whole_Order_Promo_Discount_Amount__c) {
                                wholeProDiscountAmount = qItem.Whole_Order_Promo_Discount_Amount__c * 100;
                            }
                            allPromoDiscountAmount = -((proDiscountAmount + wholeProDiscountAmount) / 100);
                            allPromoDiscountAmount = Math.round((allPromoDiscountAmount/qItem.Quantity__c) * 100) / 100;
                            allPromoDiscountAmount = allPromoDiscountAmount + qItem.Unit_Price__c;
                            allPromoDiscountAmount = Math.round(allPromoDiscountAmount * 100) / 100;
                            if (qItem.List_Price__c == price || price == null || price == '' || price == undefined || allPromoDiscountAmount == price) {
                                console.log("价格一致，不更新");
                            } else {
								qItem.List_Price__c = price;
                                isNotChange = false;
                                var toastEvent = $A.get("e.force:showToast");
                                toastEvent.setParams({
                                    "title": "Warning!",
                                    "message": `The price of [${qItem.ProductCode__c}] has been changed.Please re-enter this order.`,
                                    "type": "Warning",
                                     "mode": "sticky"
                                });
                                toastEvent.fire();
                                component.set("v.quotationItemList", []);
                                return;
                            }
                        });
                        // if (isNotChange) {
                        //     component.set("v.quotationItemList", quotationItemList);
                        // }
					}
				} else {
					console.log(response.getError());
	            }
	            component.set("v.isBusy", false);
			});
			$A.enqueueAction(action);
		});
	},
	delay: function(ms) {
        return new Promise(function(resolve) {
            setTimeout(resolve, ms);
        });
    },
    getProductSellable:  function (component, event, helper) {
        component.set("v.isBusy", true);
        var isNotChange = true;
        var action = component.get("c.getProductSellable");
        action.setParam("recordId", component.get("v.recordId"));
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                if (result != '') {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Warning!",
                        "message": result,
                        "type": "Warning",
                            "mode": "sticky"
                    });
                    toastEvent.fire();
                    component.set("v.quotationItemList", []);
                    return;
                }
            } else {
                console.log(response.getError());
            }
            component.set("v.isBusy", false);
        });
        $A.enqueueAction(action);
	},

    validateShipDate: function (component) {
        var quotationItemList = component.get("v.quotationItemList");
        quotationItemList.forEach(function (qItem) {
        	qItem.showMessage =false;
        });
        component.set("v.quotationItemList", quotationItemList);
		let allValidate = false;
        let shipDateEles = component.find("shipdate");
        if (shipDateEles != null && shipDateEles != undefined) {
            if (!Array.isArray(shipDateEles) && shipDateEles.reportValidity) {
                allValidate = shipDateEles.reportValidity();
            } else {
                allValidate = shipDateEles.reduce(function (validSoFar, inputCmp) {
                    inputCmp.reportValidity();
                    return validSoFar && inputCmp.checkValidity();
                }, true);
            }
        } else {
            allValidate = true;
        }
		return allValidate;
	}
});