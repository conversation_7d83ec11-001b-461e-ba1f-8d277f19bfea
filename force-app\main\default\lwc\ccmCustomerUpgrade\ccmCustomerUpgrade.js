import { LightningElement, track, api, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import { CurrentPageReference } from 'lightning/navigation';
import { getRecord } from 'lightning/uiRecordApi';
import { IsConsoleNavigation, getFocusedTabInfo, closeTab } from 'lightning/platformWorkspaceApi';
import USER_ID from '@salesforce/user/Id';
import NAME_FIELD from '@salesforce/schema/User.Name';
import EMAIL_FIELD from '@salesforce/schema/User.Email';
import createNewCustomer from '@salesforce/apex/CCM_CustomerUpgradeController.createNewCustomer';
import searchRecordOwner from '@salesforce/apex/CCM_CustomerUpgradeController.searchRecordOwner';
import searchProEndAccount from '@salesforce/apex/CCM_CustomerUpgradeController.searchProEndAccount';
import searchProEndContact from '@salesforce/apex/CCM_CustomerUpgradeController.searchProEndContact';
import searchCustomerAndContactInfo from '@salesforce/apex/CCM_CustomerUpgradeController.searchCustomerAndContactInfo';

const endUserAccount = 'End User Account';
const proEndCustomer = 'Commercial User';


export default class CcmCustomerUpgrade extends NavigationMixin(LightningElement) {
    
    @track options = [
        { label: 'EGO', value: 'EGO' },
        { label: 'Skil', value: 'Skil' },
        { label: 'SkilSaw', value: 'SkilSaw' },
        { label: 'FLEX', value: 'FLEX' }
    ];

    @wire(CurrentPageReference)
    currentPageReference;

    // 获取当前用户信息
    @wire(getRecord, { recordId: USER_ID, fields: [NAME_FIELD, EMAIL_FIELD] })
    currentUser;
    @wire(IsConsoleNavigation) isConsoleNavigation;
    @api selectedCustomers = [];
    @track customerOption;
    @track contactOption;
    @track isCreateNewCustomer;
    @track isChooseExistingCustomer;
    @track isCreateNewContact;
    @track isChooseExistingContact;
    @track shippingAddress = {};
    @track recordOwnerOptions;
    @track accountOptions;
    @track contactOptions;
    @track recordOwner;
    @track isProcessing = false;
    @track existingCustomerInfo = {};
    @track existingContactInfo = {};
    @track originalContactInfo = {};
    @track originalCustomerInfo = {};
    @track referenceContactInfo = {};
    @track brand = [];
    

    connectedCallback() {

        if (this.currentPageReference && this.currentPageReference.state) {
            const selectedCustomersParam = this.currentPageReference.state.c__selectedCustomers;
            if (selectedCustomersParam) {
                try {
                    this.selectedCustomers = JSON.parse(selectedCustomersParam);
                } catch (error) {
                    this.showToast('Error', 'select customers error', 'error');
                }
            }

            this.isProcessing = true;
            const source = this.selectedCustomers[0].source;
            const originalAccountId = this.selectedCustomers.filter(data => data.accountRecordType == endUserAccount).length > 0 ? this.selectedCustomers.filter(data => data.accountRecordType == endUserAccount)[0].accountId : undefined;
            const originalContactId = this.selectedCustomers.filter(data => data.accountRecordType == endUserAccount).length > 0 ? this.selectedCustomers.filter(data => data.accountRecordType == endUserAccount)[0].Id : undefined;
            const existingConatctId = this.selectedCustomers.filter(data => data.accountRecordType == proEndCustomer).length > 0 ? this.selectedCustomers.filter(data => data.accountRecordType == proEndCustomer)[0].Id : undefined;
            searchCustomerAndContactInfo({originalContactId: originalContactId, existingConatctId: existingConatctId, source: source, originalAccountId: originalAccountId}).then(result=>{
                if(result.status === 'E'){
                    this.showToast('Error', result.message, 'error');
                    this.isProcessing = false;
                    return;
                }
                const originalData = result.contactList.filter(item => item.Account.RecordType.Name  == endUserAccount).length > 0 ? result.contactList.filter(item => item.Account.RecordType.Name  == endUserAccount)[0] : undefined;
                const existingData = result.contactList.filter(item => item.Account.RecordType.Name  == proEndCustomer).length > 0 ? result.contactList.filter(item => item.Account.RecordType.Name  == proEndCustomer)[0] : undefined;

                this.referenceContactInfo.firstName = originalData.FirstName;
                this.referenceContactInfo.lastName = originalData.LastName;
                this.referenceContactInfo.email = originalData.Email;
                this.referenceContactInfo.phone = originalData.Phone;

                if(this.selectedCustomers.length == 1){
                    if(result.isHaveCompany){
                        this.existingCustomerInfo = result.existingAccount;
                        this.isCreateNewCustomer = false;
                        this.isChooseExistingCustomer = true;
                    }else{
                        this.isCreateNewCustomer = true;
                        this.isChooseExistingCustomer = false;
                    }
                    this.isCreateNewContact = true;
                    this.isChooseExistingContact = false;
                    this.originalCustomerInfo.recordType = proEndCustomer;
                    this.originalCustomerInfo.accountId = originalData.AccountId;
                    this.originalCustomerInfo.customerName = originalData.Account.Organization_Name__c;
                    this.originalCustomerInfo.brand = originalData.Account.Product_Type__c.split(';');
                    this.shippingAddress.city = originalData.Account.ShippingAddress.city;
                    this.shippingAddress.state = originalData.Account.ShippingAddress.state;
                    this.shippingAddress.postalCode = originalData.Account.ShippingAddress.postalCode;
                    this.shippingAddress.country = originalData.Account.ShippingAddress.country;
                    this.shippingAddress.street = originalData.Account.ShippingAddress.street;

                    this.originalContactInfo.firstName = originalData.FirstName;
                    this.originalContactInfo.lastName = originalData.LastName;
                    this.originalContactInfo.email = originalData.Email;
                    this.originalContactInfo.phone = originalData.Phone;
                    
                    this.isProcessing = false;
                }else if(this.selectedCustomers.length == 2){
                    this.isCreateNewCustomer = false;
                    this.isChooseExistingCustomer = true;
                    this.isCreateNewContact = false;
                    this.isChooseExistingContact = true;
                    this.existingCustomerInfo.Name = existingData.Account.Name;

                    this.existingContactInfo.firstName = existingData.FirstName;
                    this.existingContactInfo.lastName = existingData.LastName;
                    this.existingContactInfo.email = existingData.Email;
                    this.existingContactInfo.phone = existingData.Phone;
                    
                    this.isProcessing = false;
                }else{
                    this.showToast('Error', 'select Too Many customers', 'error');
                }
            },error=>{
                this.showToast('Error', error.body.message, 'error');
                this.isProcessing = false;
            });
        }
    }

    handleCustomerOptionChange(event) {
        this.customerOption = event.target.value;
        if(this.customerOption === 'newCustomer'){
            this.isCreateNewCustomer = true;
            this.isChooseExistingCustomer = false;
        }else if(this.customerOption === 'existingCustomer'){
            this.isCreateNewCustomer = false;
            this.isChooseExistingCustomer = true;
        }
    }

    handleContactOptionChange(event) {
        this.contactOption = event.target.value;
        if(this.contactOption === 'newContact'){
            this.isCreateNewContact = true;
            this.isChooseExistingContact = false;
        }else if(this.contactOption === 'existingContact'){
            this.isCreateNewContact = false;
            this.isChooseExistingContact = true;
        }
    }

    handleCustomerNameChange(event) {
        this.originalCustomerInfo.customerName = event.target.value;
    }

    handleRecordTypeChange(event) {
        this.originalCustomerInfo.recordType = event.target.value;
    }

    handleWebsiteChange(event) {
        this.originalCustomerInfo.website = event.target.value;
    }

    handleBrandChange(event) {
        this.originalCustomerInfo.brand = event.detail.value;
    }

    handleShippingAddressChange(event) {
        this.originalCustomerInfo.shippingAddress = event.detail;
    }

    handleFirstNameChange(event) {
        this.originalContactInfo.firstName = event.target.value;
    }

    handleLastNameChange(event) {
        this.originalContactInfo.lastName = event.target.value;
    }

    handleEmailChange(event) {
        this.originalContactInfo.email = event.target.value;
    }

    handlePhoneChange(event) {
        this.originalContactInfo.phone = event.target.value;
    }

    handleFirstNameChange2(event) {
        this.existingContactInfo.firstName = event.target.value;
    }

    handleLastNameChange2(event) {
        this.existingContactInfo.lastName = event.target.value;
    }

    handleEmailChange2(event) {
        this.existingContactInfo.email = event.target.value;
    }

    handlePhoneChange2(event) {
        this.existingContactInfo.phone = event.target.value;
    }
    
    handleAccountSelected(event) {
        let _data = JSON.parse(event.detail);
        this.existingCustomerInfo = _data;
    }

    handleAccountInputChange(event){
        searchProEndAccount({searchTerm:event.detail}).then(data=>{
            let result = [];
            result = data.map(item=>{
                return {
                    label: item.Name,
                    value: item.Id,
                    Name: item.Name,
                    description: item.Id,
                }
            });
            this.accountOptions = result;
        },error=>{
            this.showToast('Error', error.body.message, 'error');
        });
    }

    handleContactSelected(event) {
        let _data = JSON.parse(event.detail);
        this.existingContactInfo = _data;
    }

    handleContactInputChange(event){
        this.existingContactInfo = {};
        // TODO 限定account id
        searchProEndContact({searchTerm:event.detail}).then(data=>{
            let result = [];
            result = data.map(item=>{
                return {
                    label: item.Name,
                    value: item.Id,
                    Name: item.Name,
                    description: item.Id,
                }
            });
            this.contactOptions = result;
        },error=>{
            this.showToast('Error', error.body.message, 'error');
        });
    }

    handleRecordOwnerSelected(event) {
        let _data = JSON.parse(event.detail);
        this.recordOwner = _data;
    }

    handleRecordOwnerSearchInputChange(event) {
        this.recordOwner = {};
        searchRecordOwner({searchTerm:event.detail}).then(data=>{
            let result = [];
            result = data.map(item=>{
                return {
                    label: item.Name,
                    value: item.Id,
                    // Name: item.Name,
                    description: item.Id
                }
            });
            this.recordOwnerOptions = result;
        },error=>{
            this.showToast('Error', error.body.message, 'error');
        });
    }

    handleCancel() {
        this[NavigationMixin.Navigate]({
            type: 'standard__component',
            attributes: {
                componentName: 'c__ccmCustomerSearch'
            }
        });
    }

    async handleUpgrade(event) {
        if (!this.validateForm()) {
            return;
        }

        this.isProcessing = true;
        try{
            if(this.selectedCustomers.length == 1){
                const upgradeParams = {
                    recordOwnerId: this.recordOwner.value,
                    accountId: this.originalCustomerInfo.accountId,
                    customerName: this.originalCustomerInfo.customerName,
                    website: this.originalCustomerInfo.website,
                    brand: this.originalCustomerInfo.brand,
                    shippingCity: this.shippingAddress.city,
                    shippingState: this.shippingAddress.state,
                    shippingPostalCode: this.shippingAddress.postalCode,
                    shippingCountry: this.shippingAddress.country,
                    shippingStreet: this.shippingAddress.street,
                    firstName: this.originalContactInfo.firstName,
                    lastName: this.originalContactInfo.lastName,
                    email: this.originalContactInfo.email,
                    phone: this.originalContactInfo.phone
                };
                console.log('upgradeParams:', upgradeParams);

                const result = await createNewCustomer({upgradeParams: upgradeParams});
                if (result.status === 'S'){
                    this.showToast('Success', 'Customer And Contact created successfully', 'success');
                    setTimeout(() => {
                        if (this.isConsoleNavigation) {
                            const { tabId } = getFocusedTabInfo();
                            closeTab(tabId);
                        }
                        this[NavigationMixin.Navigate]({
                            type: 'standard__recordPage',
                            attributes: {
                                recordId: result.newContactId,
                                objectApiName: 'Contact',
                                actionName: 'view'
                            }
                        });
                    }, 1000);
                    this.isProcessing = false;
                }else{
                    this.isProcessing = false;
                    this.showToast('Error', result.message, 'error');
                }
            }else if(this.selectedCustomers.length == 2){
                if(this.customerOption === 'existingCustomer' && this.contactOption === 'existingContact'){
                    const mergeParams = {
                        existingAccountId: this.existingCustomerInfo.Id,
                        existingContactId: this.existingContactInfo.Id,
                        recordOwnerId: this.recordOwner.value,
                        firstName: this.existingContactInfo.firstName,
                        lastName: this.existingContactInfo.lastName,
                        email: this.existingContactInfo.email,
                        phone: this.existingContactInfo.phone
                    };
                    const result = await mergeCustomers({mergeParams: mergeParams});
                    if (result.status === 'S'){
                        this.showToast('Success', 'Contact Merge successfully', 'success');
                        setTimeout(() => {
                            if (this.isConsoleNavigation) {
                                const { tabId } = getFocusedTabInfo();
                                closeTab(tabId);
                            }
                            this[NavigationMixin.Navigate]({
                                type: 'standard__recordPage',
                                attributes: {
                                    recordId: result.newContactId,
                                    objectApiName: 'Contact',
                                    actionName: 'view'
                                }
                            });
                        }, 1000);
                        this.isProcessing = false;
                    }else{
                        this.showToast('Error', result.message, 'error');
                    }
                }else{
                    this.showToast('Warning', 'Please select correct customer to upgrade.', 'warning');
                }
            }
            this.isProcessing = false;
        }catch(error){
            this.showToast('Error', (error.body?.message || error.message), 'error');
            this.isProcessing = false;
        }
    }

    validateForm() {
        let isValid = true;

        if(this.customerOption === 'newCustomer'){
            const customerName = this.template.querySelector('#customerName');
            if (!customerName.reportValidity()) {
                isValid = false;
            }
        }

        return isValid;
    }

    showToast(title, message, variant) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant
        });
        this.dispatchEvent(event);
    }
}
