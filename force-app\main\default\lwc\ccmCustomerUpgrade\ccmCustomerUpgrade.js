import { LightningElement, track, api, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import { CurrentPageReference } from 'lightning/navigation';
import { getRecord } from 'lightning/uiRecordApi';
import { IsConsoleNavigation, getFocusedTabInfo, closeTab } from 'lightning/platformWorkspaceApi';
import USER_ID from '@salesforce/user/Id';
import NAME_FIELD from '@salesforce/schema/User.Name';
import EMAIL_FIELD from '@salesforce/schema/User.Email';
import createNewCustomer from '@salesforce/apex/CCM_CustomerUpgradeController.createNewCustomer';
import searchRecordOwner from '@salesforce/apex/CCM_CustomerUpgradeController.searchRecordOwner';
import searchProEndAccount from '@salesforce/apex/CCM_CustomerUpgradeController.searchProEndAccount';
import searchProEndContact from '@salesforce/apex/CCM_CustomerUpgradeController.searchProEndContact';
import searchExistingContact from '@salesforce/apex/CCM_CustomerUpgradeController.searchExistingContact';

const endUserAccount = 'End User Account';
const customertype = 'Residential'
const proEndCustomer = 'Commerical User';


export default class CcmCustomerUpgrade extends NavigationMixin(LightningElement) {
    
    @wire(CurrentPageReference)
    currentPageReference;

    // 获取当前用户信息
    @wire(getRecord, { recordId: USER_ID, fields: [NAME_FIELD, EMAIL_FIELD] })
    currentUser;
    @wire(IsConsoleNavigation) isConsoleNavigation;
    @api selectedCustomers = [];
    @track customerOption;
    @track contactOption;
    @track isCreateNewCustomer;
    @track isChooseExistingCustomer;
    @track isCreateNewContact;
    @track isChooseExistingContact;
    @track customerName;
    @track recordType;
    @track website;
    @track shippingAddress;
    @track keepOriginalAccount;
    @track recordTypeDisabled;
    @track recordOwnerOptions;
    @track accountOptions;
    @track contactOptions;
    @track recordOwner;
    @track account;
    @track contact;
    @track isProcessing = false;
    @track existingContactInfo;
    @track originalContactInfo;
    @track originalCustomerInfo;
    

    connectedCallback() {

        if (this.currentPageReference && this.currentPageReference.state) {
            const selectedCustomersParam = this.currentPageReference.state.c__selectedCustomers;
            if (selectedCustomersParam) {
                try {
                    this.selectedCustomers = JSON.parse(selectedCustomersParam);
                } catch (error) {
                    this.showToast('Error', 'select customers error', 'error');
                }
            }

            this.isProcessing = true;
            searchExistingContact({contactId:this.selectedCustomers[0].Id}).then(data=>{
                if(this.selectedCustomers.length == 1){
                    if(this.selectedCustomers[0].accountRecordType === endUserAccount){
                        this.isCreateNewCustomer = true;
                        this.isChooseExistingCustomer = false;
                        this.isCreateNewContact = true;
                        this.isChooseExistingContact = false;
                        this.recordType = proEndCustomer;
                        this.recordTypeDisabled = true;
                        this.firstName = data.FirstName;
                        this.lastName = data.LastName;
                        this.email = data.Email;
                        this.phone = data.Phone;
                        this.originalContactInfo = data;
                        this.isProcessing = false;
                    }else{
                        this.showToast('Error', 'select correct customer', 'error');
                        this.isProcessing = false;
                    }
                }else if(this.selectedCustomers.length == 2){

                }else{
                    this.showToast('Error', 'select Too Many customers', 'error');
                }
            },error=>{
                this.showToast('Error', error.body.message, 'error');
                this.isProcessing = false;
            });
        }
    }

    // /**
    //  * 设置初始Record Owner为当前用户
    //  */
    // setInitialRecordOwner() {
    //     if (this.currentUser && this.currentUser.data) {
    //         this.initialRecordOwner = {
    //             Id: USER_ID,
    //             Name: this.currentUser.data.fields.Name.value,
    //             Email: this.currentUser.data.fields.Email.value,
    //             label: this.currentUser.data.fields.Name.value,
    //             value: USER_ID,
    //             description: this.currentUser.data.fields.Email.value
    //         };
    //     } else {
    //         // 如果用户信息还未加载，设置一个默认值
    //         this.initialRecordOwner = {
    //             Id: USER_ID,
    //             Name: 'Current User',
    //             label: 'Current User',
    //             value: USER_ID,
    //             description: 'Loading...'
    //         };
    //     }
    // }

    get isShowCustomerDetail() {
        return this.account && this.account != null;
    }

    get isShowContactDetail() {
        return this.contact && this.contact != null;
    }

    renderedCallback() {
        // // 如果用户数据已加载但初始Record Owner还未正确设置，重新设置
        // if (this.currentUser && this.currentUser.data &&
        //     (!this.initialRecordOwner || this.initialRecordOwner.Name === 'Current User')) {
        //     this.setInitialRecordOwner();
        // }
    }

    handleCustomerOptionChange(event) {
        this.customerOption = event.target.value;
        if(this.customerOption === 'newCustomer'){
            this.isCreateNewCustomer = true;
            this.isChooseExistingCustomer = false;
        }else if(this.customerOption === 'existingCustomer'){
            this.isCreateNewCustomer = false;
            this.isChooseExistingCustomer = true;
        }
    }

    handleContactOptionChange(event) {
        this.contactOption = event.target.value;
        if(this.contactOption === 'newContact'){
            this.isCreateNewContact = true;
            this.isChooseExistingContact = false;
        }else if(this.contactOption === 'existingContact'){
            this.isCreateNewContact = false;
            this.isChooseExistingContact = true;
        }
    }

    handleCustomerNameChange(event) {
        this.customerName = event.target.value;
    }

    handleRecordTypeChange(event) {
        this.recordType = event.target.value;
    }

    handleWebsiteChange(event) {
        this.website = event.target.value;
    }

    handleShippingAddressChange(event) {
        this.shippingAddress = event.detail;
    }

    handleFirstNameChange(event) {
        this.firstName = event.target.value;
    }

    handleLastNameChange(event) {
        this.lastName = event.target.value;
    }

    handleEmailChange(event) {
        this.email = event.target.value;
    }

    handlePhoneChange(event) {
        this.phone = event.target.value;
    }

    handleKeepOriginalAccountChange(event) {
        this.keepOriginalAccount = event.target.checked;
    }
    
    handleAccountSelected(event) {
        let _data = JSON.parse(event.detail);
        this.account = _data;
    }

    handleAccountInputChange(event){
        this.account = null;
        searchProEndAccount({searchTerm:event.detail}).then(data=>{
            let result = [];
            result = data.map(item=>{
                return {
                    label: item.Name,
                    value: item.Id,
                    Name: item.Name,
                    description: item.Id,
                }
            });
            this.accountOptions = result;
        },error=>{
            this.showToast('Error', error.body.message, 'error');
        });
    }

    handleContactSelected(event) {
        let _data = JSON.parse(event.detail);
        this.contact = _data;
    }

    handleContactInputChange(event){
        this.contact = {};
        // TODO 限定account id
        searchProEndContact({searchTerm:event.detail}).then(data=>{
            let result = [];
            result = data.map(item=>{
                return {
                    label: item.Name,
                    value: item.Id,
                    Name: item.Name,
                    description: item.Id,
                }
            });
            this.contactOptions = result;
        },error=>{
            this.showToast('Error', error.body.message, 'error');
        });
    }

    handleRecordOwnerSelected(event) {
        let _data = JSON.parse(event.detail);
        this.recordOwner = _data;
    }

    handleRecordOwnerSearchInputChange(event) {
        this.recordOwner = {};
        searchRecordOwner({searchTerm:event.detail}).then(data=>{
            let result = [];
            result = data.map(item=>{
                return {
                    label: item.Name,
                    value: item.Id,
                    // Name: item.Name,
                    description: item.Id
                }
            });
            this.recordOwnerOptions = result;
        },error=>{
            this.showToast('Error', error.body.message, 'error');
        });
    }



    handleCancel() {
        this[NavigationMixin.Navigate]({
            type: 'standard__component',
            attributes: {
                componentName: 'c__ccmCustomerSearch'
            }
        });
    }

    handleUpgrade(event) {
        // if (!this.validateForm()) {
        //     return;
        // }

        this.isProcessing = true;

        const upgradeParams = {
            recordOwnerId: this.recordOwner.value,
            customerName: this.customerName,
            website: this.website,
            shippingCity: this.shippingAddress.city,
            shippingState: this.shippingAddress.state,
            shippingPostalCode: this.shippingAddress.postalCode,
            shippingCountry: this.shippingAddress.country,
            shippingStreet: this.shippingAddress.street,
            firstName: this.firstName,
            lastName: this.lastName,
            email: this.email,
            phone: this.phone
        };
        console.log('upgradeParams:', upgradeParams);

        if(this.selectedCustomers.length == 1){
            // createNewCustomer({upgradeParams: upgradeParams}).then(result=>{
            //     if (result.status === 'S'){
            //         this.showToast('Success', 'Customer And Contact created successfully', 'success');
            //         setTimeout(() => {
            //             this[NavigationMixin.Navigate]({
            //                 type: 'standard__recordPage',
            //                 attributes: {
            //                     recordId: result.newContactId,
            //                     objectApiName: 'Contact',
            //                     actionName: 'view'
            //                 }
            //             });
            //         }, 1000);
            //         if (this.isConsoleNavigation) {
            //             const { tabId } = getFocusedTabInfo();
            //             closeTab(tabId);
            //         }
                    
            //     }else{
            //         this.showToast('Error', result.message, 'error');
            //     }
            // },error=>{
            //     this.showToast('Error', error.body.message, 'error');
            // });
        }else if(this.selectedCustomers.length == 2){

        }else{

        }
        setTimeout(() => {
        
        }, 2000);
        this.isProcessing = false;
    }

    // validateForm() {
    //     let isValid = true;
    //     let errorMessages = [];

    //     // Validate customer section
    //     if (this.isCreateNewCustomer) {
    //         if (!this.newCustomer.name || this.newCustomer.name.trim() === '') {
    //             isValid = false;
    //             errorMessages.push('Customer Name is required');
    //         }
    //     }

    //     // Validate contact section
    //     if (this.isCreateNewContact) {
    //         if (!this.contactDisplayName || this.contactDisplayName.trim() === '') {
    //             isValid = false;
    //             errorMessages.push('Contact Name is required');
    //         }
    //     }

    //     if (!isValid) {
    //         this.showToast('Validation Error',
    //             `Please fix the following errors: ${errorMessages.join(', ')}`,
    //             'error');
    //     }

    //     return isValid;
    // }

    showToast(title, message, variant) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant
        });
        this.dispatchEvent(event);
    }
}
