/**
 * <AUTHOR>
 * @date 2023-08-29
 * @description Query product information
 */
public without sharing class CCM_PurchaseOrderUpload_Order {
    
    public static List<ReturnMessage> errorMessages = new List<ReturnMessage>();

    public static List<ProductInfoWrapper> generateProductInfo(List<POItemInfo> poItemInfos, String productFilter, String customerId, String orderType) {
        Set<String> productCodes = new Set<String>();
        for(POItemInfo poItem : poItemInfos) {
            if(String.isNotBlank(poItem.ProductCode)) {
                productCodes.add(poItem.ProductCode);
            }
        }

        Map<String, Product2> productMap = getProductMap(productCodes, productFilter);

        Account acc = getCustomerInfo(customerId);
        String orgCode = CCM_Constants.ORG_CODE_CNA;
        if(String.isNotBlank(acc.ORG_Code__c)) {
            orgCode = acc.ORG_Code__c;
        }

        Boolean isExpire = true;
        Date Expirate = Util.parseDate(Label.Package_Order_Expire_Date, UserInfo.getLocale());
        Date Today = System.today();
        if(Today < Expirate) {
            isExpire = false;
        }

        Set<String> prodIds = new Set<String>();
        for(Product2 prod : productMap.values()) {
            prodIds.add(prod.Id);
        }

        Map<String, PricebookEntry> prodEntryMap = new Map<String, PricebookEntry>();
        Map<String, List<CCM_Quotation_ProductSelectCtl.PromotionData>> promotionsPerProductMap = new Map<String, List<CCM_Quotation_ProductSelectCtl.PromotionData>>();
        if('Y' == orderType) {
            if(CCM_Constants.ORG_CODE_CCA == orgCode) {
                prodEntryMap = getDropShipPriceBookPerProductCA(prodIds, customerId);
            }
            else {
                prodEntryMap = getPriceBookEntryPerProduct(prodIds, customerId, 'Dropship Sales');
            }
            promotionsPerProductMap = getPromotionsPerProduct(prodIds, customerId, false, true);
        }
        else {
            prodEntryMap = getPriceBookEntryPerProduct(prodIds, customerId, 'Sales');
            promotionsPerProductMap = getPromotionsPerProduct(prodIds, customerId, false, false);
        }

        List<ProductInfoWrapper> wrappers = new List<ProductInfoWrapper>();
        for(POItemInfo poItem : poItemInfos) {
            if(productMap.containsKey(poItem.ProductCode)) {
                Product2 product = productMap.get(poItem.ProductCode);
                ProductInfoWrapper wrapper = new ProductInfoWrapper();
                wrapper.ProductId = product.Id;
                wrapper.Name = product.Name;
                wrapper.ProductCode = product.ProductCode;
                wrapper.Quantity = poItem.Quantity;
                wrapper.InitData = new CCM_Quotation_ProductSelectCtl.InitData();
                wrapper.InitData.product = product;
                wrapper.InitData.OrgCode = orgCode;
                wrapper.InitData.ExpirateDate = isExpire;
                if(prodEntryMap.containsKey(product.Id)) {
                    wrapper.InitData.priceBookEntry = prodEntryMap.get(product.Id);
                    wrapper.UnitPrice = wrapper.InitData.priceBookEntry.UnitPrice;
                    wrapper.SubTotal = wrapper.UnitPrice * wrapper.Quantity;
                }
                if(promotionsPerProductMap.containsKey(product.Id)) {
                    wrapper.InitData.promotionList = promotionsPerProductMap.get(product.Id);
                }
                wrappers.add(wrapper);
                productCodes.remove(poItem.ProductCode);
            }
        }

        if(!productCodes.isEmpty()) {
            ReturnMessage msg = new ReturnMessage();
            msg.IsSuccess = false;
            List<String> productCodesNotExist = new List<String>();
            productCodesNotExist.addAll(productCodes);
            msg.ErrorMessage = String.format('Product Code {0} not Exist, Please Correct.', new List<String>{String.join(productCodesNotExist, ',')});
            errorMessages.add(msg);
        }
        return wrappers;
    }


    public static Map<String, Product2> getProductMap(Set<String> productCodes, String productFilter) {
        Map<String, Product2> productMap = new Map<String, Product2>();
        String filter = CCM_AutoMatchPickListCtl.generateCondition(productFilter, null);
        if(String.isNotBlank(filter)) {
            filter += ' AND ';
        }
        else {
            filter += ' WHERE ';
        }
        filter += ' ProductCode IN :productCodes';
        String query = 'SELECT Id, Brand_Name__c, Item_Number__c, CS_Exchange_Rate__c, ProductCode, Weight__c, OverSize__c, Name, SF_Description__c FROM Product2 ' + filter;
        List<Product2> products = (List<Product2>)Database.query(query);
        for(Product2 product : products) {
            productMap.put(product.ProductCode, product);
        }
        return productMap;
    }


    public static Account getCustomerInfo(String customerId) {
        Boolean isInnerUser = Util.isInnerUser();
        
        if(!isInnerUser) {
            User u = Util.getUserInfo(UserInfo.getUserId());
            List<Contact> contacts = [SELECT AccountId FROM Contact WHERE Id = :u.ContactId];
            if(!contacts.isEmpty()) {
                List<Account> customers = [SELECT Id, Name, ORG_Code__c FROM Account WHERE RecordType.DeveloperName = 'Channel' AND Id = :contacts[0].AccountId];
                if(!customers.isEmpty()) {
                    return customers[0];
                }
            }
        }
        else {
            List<Account> customers = [SELECT Id, Name, ORG_Code__c FROM Account WHERE RecordType.DeveloperName = 'Channel' AND Id = :customerId];
            if(!customers.isEmpty()) {
                return customers[0];
            }
        }
        return null;
    }


    public static Map<String, List<CCM_Quotation_ProductSelectCtl.PromotionData>> getPromotionsPerProduct(Set<String> prodIds, String customerId, Boolean isPortal, Boolean isDropShip) {

        Map<String, Set<String>> productPromotionMap = new Map<String, Set<String>>();
        Set<String> promotionIds = new Set<String>();
        for(Promotion_Product__c proProduct : [SELECT Product__c, Promotion2_Threshold__r.Promotion_Rule__r.Promotion__c FROM Promotion_Product__c WHERE Product__c IN :prodIds AND Promotion2_Threshold__r.Promotion_Rule__r.Promotion__r.Promotion_Status__c = 'Open']) {
            if(!productPromotionMap.containsKey(proProduct.Product__c)) {
                productPromotionMap.put(proProduct.Product__c, new Set<String>());
            }
            productPromotionMap.get(proProduct.Product__c).add(proProduct.Promotion2_Threshold__r.Promotion_Rule__r.Promotion__c);
            promotionIds.add(proProduct.Promotion2_Threshold__r.Promotion_Rule__r.Promotion__c);
        }

        Map<String, List<CCM_Quotation_ProductSelectCtl.PromotionData>> promotionsPerProductMap = new Map<String, List<CCM_Quotation_ProductSelectCtl.PromotionData>>();
        if(!promotionIds.isEmpty()) {
            Map<String, CCM_Quotation_ProductSelectCtl.PromotionData> promotionMap = getPromotionDataMap(promotionIds, prodIds, customerId, isPortal, isDropShip);
            
            for(String productId : productPromotionMap.keySet()) {
	
                Set<String> promotionSet = productPromotionMap.get(productId);
                for(String promotionId : promotionMap.keySet()) {
                    if(promotionSet.contains(promotionId)) {
                        CCM_Quotation_ProductSelectCtl.PromotionData promotionData = promotionMap.get(promotionId);
                        CCM_Quotation_ProductSelectCtl.PromotionData matchPromotionData = new CCM_Quotation_ProductSelectCtl.PromotionData();
                        matchPromotionData.promotion = promotionData.promotion;
                        matchPromotionData.windowId = promotionData.windowId;
                        for(CCM_Quotation_ProductSelectCtl.PromoRuleData ruleData : promotionData.ruleList) {
                            for(CCM_Quotation_ProductSelectCtl.PromoThresholdData thresholdData : ruleData.thresholdList) {
                                for(Promotion_Product__c promotionProudct : thresholdData.products) {
                                    if(productId == promotionProudct.Product__r.Id) {
                                        matchPromotionData.ruleList.add(ruleData);
                                    }
                                }
                            }
                        }
                        if(!matchPromotionData.ruleList.isEmpty()) {
                            if(!promotionsPerProductMap.containsKey(productId)) {
                                promotionsPerProductMap.put(productId, new List<CCM_Quotation_ProductSelectCtl.PromotionData>());
                            }
                            promotionsPerProductMap.get(productId).add(matchPromotionData);
                        }
                        
                    }
                }
            }    
        }
        return promotionsPerProductMap;
    }


    private static Map<String, CCM_Quotation_ProductSelectCtl.PromotionData> getPromotionDataMap(Set<String> promotionIds, Set<String> prodIds, String customerId, Boolean isPortal, Boolean isDropShip) {
        Map<String, CCM_Quotation_ProductSelectCtl.PromotionData> promotionMap = new Map<String, CCM_Quotation_ProductSelectCtl.PromotionData>();
        List<Promotion_Target_Customer__c> promo2CustomerList = new List<Promotion_Target_Customer__c>();
        Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id =: customerId];
        Boolean isCCA = customer.ORG_Code__c == 'CCA'? true : false;
        String str = 'SELECT Promotion__c FROM Promotion_Target_Customer__c WHERE  Promotion__r.RecordType.DeveloperName = \'Sell_In_Promotion\' AND Promotion__r.Promotion_Status__c = \'Open\' ';
        if (!isCCA) {
            str += isDropShip? 'AND Promotion__r.Is_DropShip_Promotion__c = true': 'AND Promotion__r.Non_DropShip_Promotion__c = true';
            str += ' AND (Customer__c = :customerId OR Top_Customer__c = :customerId) ';
            str += ' AND Promotion__c = :promotionIds';
            promo2CustomerList = Database.query(str);
        }else {
            promo2CustomerList = [
                SELECT Promotion__c
                FROM Promotion_Target_Customer__c
                WHERE Customer__c =:customerId
                AND Promotion__r.RecordType.DeveloperName = 'Sell_In_Promotion' 
                AND Promotion__r.Promotion_Status__c = 'Open' 
                AND Promotion__r.Is_DropShip_Promotion__c =: isDropShip
            ];
        }
        //end update by austin 2023.2.1
        
        if(promo2CustomerList.size() ==0){
            //there is no promotion for the customer
            return promotionMap;
        }
                
        Set<String> promoIds = new Set<String>();
        for(Promotion_Target_Customer__c result : promo2CustomerList){
            promoIds.add(result.Promotion__c);
        }

        Date today = System.today();
        List<String> windowStatus = new List<String>();
        windowStatus.add('In Progress');
        if(!isPortal){
            windowStatus.add('Closed For External');
        }
        List<Promotion_Window__c> promotionWindowList = [
            SELECT 
                Id,
                Promotion__c,
                Availability_in_Dealer_Portal__c
            FROM Promotion_Window__c
            WHERE Promotion__c IN:promoIds
            //AND Start_Date__c <= :today
            //AND End_Date__c >= :today           
            AND Promotion_Window_Status2__c IN :windowStatus
        ];

        if(promotionWindowList.size() ==0){
            //there is no promotion for the customer
            return promotionMap;
        }

        //the promotions with available windows
        promoIds = new Set<String>();
        Map<Id, Id> promoId2WindowId = new Map<Id, Id>();
        for(Promotion_Window__c pWindow : promotionWindowList){
            if(isPortal){
                if(pWindow.Availability_in_Dealer_Portal__c == 'Available in Portal' 
                    || pWindow.Availability_in_Dealer_Portal__c == 'Invisible in Portal'){
                    promoIds.add(pWindow.Promotion__c);
                    if(!promoId2WindowId.containsKey(pWindow.Promotion__c)){
                        promoId2WindowId.put(pWindow.Promotion__c, pWindow.Id);
                    }
                }
            }else{
                promoIds.add(pWindow.Promotion__c);
                if(!promoId2WindowId.containsKey(pWindow.Promotion__c)){
                    promoId2WindowId.put(pWindow.Promotion__c, pWindow.Id);
                }
            }        
        }

        List<Promotion2__c> promotion2List = [
            SELECT 
                Id, 
                Brands__c,
                Name,
                Promotion_Type__c,
                Promo_Code__c,
                Promotion_Code_For_External__c
            FROM Promotion2__c
            WHERE Id IN :promoIds
        ];
        Map<Id, Promotion2__c> id2PromoMap = new Map<Id, Promotion2__c>();
        for(Promotion2__c pItem : promotion2List){
            id2PromoMap.put(pItem.Id, pItem);
        }
        List<Promotion_Rule__c> promoRuleList = [
            SELECT 
                Id,
                Name,
                Promotion__c,
                (SELECT
                    Id,
                    Name,
                    Gift_Total_Quantity__c,
                    Payment_Term__c,
                    RecordTypeId,
                    RecordType.Name, 
                    RecordType.DeveloperName,
                    Promotion_Rule__c,
                    Discount_Off__c,
                    Amount_Off__c
                FROM Promotion_Offerings__r),
                (SELECT
                    Id,
                    Name,
                    Bogo_Remark__c,
                    RecordTypeId,
                    RecordType.Name,
                    RecordType.DeveloperName,
                    Minimum_Total_Amount__c,
                    Minimum_Total_Quantity__c,
                    Multiple_Control__c,
                    Buy_Qty__c,
                    Minimum_Different_Tool_Models__c,
                    Max_Different_Tool_Models__c,
                    Minimum_Whole_Order_Amount__c,
                    Maximum_Whole_Order_Amount__c,
                    Promotion_Rule__c
                FROM Thresholds__r
                ORDER BY Name)
            FROM Promotion_Rule__c
            WHERE Promotion__c IN :promoIds
        ];
        Map<Id, List<Promotion_Rule__c>> promo2RuleMap = new Map<Id, List<Promotion_Rule__c>>();
        Map<Id, Promotion_Rule__c> id2RuleMap = new Map<Id, Promotion_Rule__c>();
        Set<Id> thresholdIds = new Set<Id>();
        Set<Id> offeringIds = new Set<Id>();
        for(Promotion_Rule__c ruleItem : promoRuleList){
            if(!promo2RuleMap.containsKey(ruleItem.Promotion__c)){
                promo2RuleMap.put(ruleItem.Promotion__c, new List<Promotion_Rule__c>());
            }
            promo2RuleMap.get(ruleItem.Promotion__c).add(ruleItem);
            id2RuleMap.put(ruleItem.Id, ruleItem);
            for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                thresholdIds.add(ptItem.Id);
            }
            for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                offeringIds.add(poItem.Id);
            }
        }
        List<Promotion_Product__c> allPromotionProductList = [
            SELECT 
                Id,
                Minimum_Amount__c,
                Multiple_Control__c,
                Maximum_Amount__c,
                Minimum_Quantity__c,
                Maximum_Quantity__c,
                Gift_Quantity__c,
                Number__c,
                Chervon_Funding__c,
                Increment_For_Free_Goods__c,
                PMAPP__c,
                Promotion2_Threshold__c,
                Promotion2_Offering__c,
                Additional_Discount__c,
                Product__c,
                Product__r.Name,
                Product__r.Brand_Name__c,
                Product__r.CS_Exchange_Rate__c,
                Product__r.ProductCode,
                Product__r.SF_Description__c,
                Product__r.Full_Pallet_Quantity__c,
                Product__r.RecordType.DeveloperName
            FROM Promotion_Product__c
            WHERE Promotion2_Threshold__c IN :thresholdIds OR Promotion2_Offering__c IN :offeringIds
        ];
        Map<Id, List<Promotion_Product__c>> threshold2ProductMap = new Map<Id, List<Promotion_Product__c>>();
        Map<Id, List<Promotion_Product__c>> offering2ProductMap = new Map<Id, List<Promotion_Product__c>>();        
        for(Promotion_Product__c ppItem : allPromotionProductList){
            if(String.isNotEmpty(ppItem.Promotion2_Threshold__c)){
                if(!threshold2ProductMap.containsKey(ppItem.Promotion2_Threshold__c)){
                    threshold2ProductMap.put(ppItem.Promotion2_Threshold__c, new List<Promotion_Product__c>());
                }
                threshold2ProductMap.get(ppItem.Promotion2_Threshold__c).add(ppItem);
            }
            if(String.isNotEmpty(ppItem.Promotion2_Offering__c)){
                if(!offering2ProductMap.containsKey(ppItem.Promotion2_Offering__c)){
                    offering2ProductMap.put(ppItem.Promotion2_Offering__c, new List<Promotion_Product__c>());
                }
                offering2ProductMap.get(ppItem.Promotion2_Offering__c).add(ppItem);
            }
        }

        List<Promotion_Product__c> thresholdProductList = [
            SELECT 
                Id,
                Multiple_Control__c,
                Minimum_Amount__c,
                Maximum_Amount__c,
                Minimum_Quantity__c,
                Maximum_Quantity__c,
                Gift_Quantity__c,
                Number__c,
                Increment_For_Free_Goods__c,
                Chervon_Funding__c,
                PMAPP__c,
                Promotion2_Threshold__c,
                Promotion2_Offering__c,
                product__r.CS_Exchange_Rate__c,
                Product__c                
            FROM Promotion_Product__c
            WHERE Promotion2_Threshold__c IN :thresholdIds AND Product__c IN :prodIds
        ];
        System.debug('***** thresholdIds:' + thresholdIds);
        System.debug('***** thresholdProductList:' + thresholdProductList);
        if(thresholdProductList.size() == 0){
            //there is no promotion for the product
            return promotionMap;
        }

        //available threshold records for the selected product
        Set<Id> availableThresholdIds = new Set<Id>();
        for(Promotion_Product__c ppItem : thresholdProductList){
            availableThresholdIds.add(ppItem.Promotion2_Threshold__c);
        }
        List<Promotion_Threshold__c> availableThresholdList = [
            SELECT 
                Id,
                Promotion_Rule__c,
                Multiple_Control__c,
                Buy_Qty__c,
                Promotion_Rule__r.Promotion__c
            FROM Promotion_Threshold__c
            WHERE Id IN :availableThresholdIds
        ];
        System.debug('***** availableThresholdList:' + availableThresholdList);
        Set<Id> availablePromoIds = new Set<Id>();
        Set<Id> availableRuleIds = new Set<Id>();
        for(Promotion_Threshold__c ptItem : availableThresholdList){
            availablePromoIds.add(ptItem.Promotion_Rule__r.Promotion__c);
            availableRuleIds.add(ptItem.Promotion_Rule__c);
        }
        System.debug('***** availablePromoIds:' + availablePromoIds);

        Set<String> brandSet = new Set<String>();
        Set<Id> productIdSet = new Set<Id>();
        Set<Id> merchandisingProductIdSet = new Set<Id>();
        for(Id pId : availablePromoIds){
            for(Promotion_Rule__c ruleItem : promo2RuleMap.get(pId)){
                if(!availableRuleIds.contains(ruleItem.Id)){
                    continue;
                }
                for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                    for(Promotion_Product__c ppItem : threshold2ProductMap.get(ptItem.Id)){
                        brandSet.add(ppItem.Product__r.Brand_Name__c);
                        productIdSet.add(ppItem.Product__c);
                        if(ppItem.Product__r.RecordType.DeveloperName == 'Amware_Merchandising'
                            || ppItem.Product__r.RecordType.DeveloperName == 'Oracle_Merchandising'){
                            merchandisingProductIdSet.add(ppItem.Product__c);
                        }
                    }
                }
                for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                    if(offering2ProductMap.get(poItem.Id)==null) continue;
                    for(Promotion_Product__c ppItem : offering2ProductMap.get(poItem.Id)){
                        brandSet.add(ppItem.Product__r.Brand_Name__c);
                        productIdSet.add(ppItem.Product__c);
                        if(ppItem.Product__r.RecordType.DeveloperName == 'Amware_Merchandising'
                            || ppItem.Product__r.RecordType.DeveloperName == 'Oracle_Merchandising'){
                            merchandisingProductIdSet.add(ppItem.Product__c);
                        }
                    }
                }
            }
        }

        Map<Id,PricebookEntry> productId2PriceBookEntryMap = CCM_Quotation_ProductSelectCtl.getProduc2tPriceBookEntryMap(productIdSet, brandSet, customerId, isDropShip);
        Map<Id,PricebookEntry> merchProductId2PriceBookEntryMap = CCM_Quotation_ProductSelectCtl.getMerchProduc2tPriceBookEntryMap(merchandisingProductIdSet);

        for(Id pId : availablePromoIds){
            CCM_Quotation_ProductSelectCtl.PromotionData pData = new CCM_Quotation_ProductSelectCtl.PromotionData();
            pData.promotion = id2PromoMap.get(pId);
            pData.windowId = promoId2WindowId.get(pId);
            for(Promotion_Rule__c ruleItem : promo2RuleMap.get(pId)){
                if(!availableRuleIds.contains(ruleItem.Id)){
                    continue;
                }
                CCM_Quotation_ProductSelectCtl.PromoRuleData rData = new CCM_Quotation_ProductSelectCtl.PromoRuleData();
                rData.ruleId = ruleItem.Id;
                rData.ruleName = ruleItem.Name;
                for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                    CCM_Quotation_ProductSelectCtl.PromoThresholdData ptData = new CCM_Quotation_ProductSelectCtl.PromoThresholdData();
                    ptData.threshold = ptItem;
                    ptData.products = threshold2ProductMap.get(ptItem.Id);
                    if(ptData.products != null){
                        for(Promotion_Product__c ppItem : ptData.products){
                            PricebookEntry pbItem = productId2PriceBookEntryMap.get(ppItem.Product__c);
                            if(merchandisingProductIdSet.contains(ppItem.Product__c)){
                                pbItem = merchProductId2PriceBookEntryMap.get(ppItem.Product__c);
                            }
                            if(pbItem!=null){
                                ptData.priceBookEntrys.add(pbItem);
                            }else{
                                ptData.priceBookEntrys.add(new PricebookEntry());
                            }
                        }
                    }                    
                    rData.thresholdList.add(ptData);                   
                }
                for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                    CCM_Quotation_ProductSelectCtl.PromoOfferingData poData = new CCM_Quotation_ProductSelectCtl.PromoOfferingData();
                    poData.offering = poItem;
                    poData.products = offering2ProductMap.get(poItem.Id);
                    if(poData.products != null){
                        for(Promotion_Product__c ppItem : poData.products){
                            PricebookEntry pbItem = productId2PriceBookEntryMap.get(ppItem.Product__c);
                            if(merchandisingProductIdSet.contains(ppItem.Product__c)){
                                pbItem = merchProductId2PriceBookEntryMap.get(ppItem.Product__c);
                            }
                            if(pbItem != null){
                                poData.priceBookEntrys.add(pbItem);
                            }else{
                                poData.priceBookEntrys.add(new PricebookEntry());
                            }
                        }
                    }                    
                    rData.offeringList.add(poData);
                }
                pData.ruleList.add(rData);
            }
            promotionMap.put(pId, pData);
        }
        System.debug('promotionList======'+promotionMap);
        return promotionMap;
    }


    public static Map<String, PricebookEntry> getPriceBookEntryPerProduct(Set<String> prodIds, String customerId, String type) {

        Set<String> brands = new Set<String>();
        List<Product2> products = [SELECT Id, Brand_Name__c, Item_Number__c, CS_Exchange_Rate__c, ProductCode, Weight__c, OverSize__c FROM Product2 WHERE Id IN :prodIds];
        for(Product2 prod : products) {
            if(String.isNotBlank(prod.Brand_Name__c)) {
                brands.add(prod.Brand_Name__c);
            }
        }

        Map<String, Sales_Program__c> spMap = new Map<String, Sales_Program__c>();
        Map<String, Sales_Program__c> brandSPMap = new Map<String, Sales_Program__c>();
        List<Sales_Program__c> authBrandList = [
                    SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c, 
                            Approval_Status__c, IsDeleted, Price_Book_Mapping__r.Name,
                            Price_Book__c, Contract_Price_List_Name__c, Contract_Price_Book__c,
                            RecordType.DeveloperName
                    FROM Sales_Program__c 
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c IN: brands];
        
        for(Sales_Program__c authBrand : authBrandList) {
            if(type == 'Sales') {
                if(authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_DEVELOPER_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_DEVELOPER_NAME) {
                    brandSPMap.put(authBrand.Brands__c, authBrand);
                    spMap.put(authBrand.Id, authBrand);
                }
            }
            if(type == 'Service') {
                if(authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_DEVELOPER_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME) {
                    brandSPMap.put(authBrand.Brands__c, authBrand);
                    spMap.put(authBrand.Id, authBrand);
                }
            }
            if(type == 'Dropship Sales') {
                if(authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_STANDARD_DEVELOPER_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_CUSTOMIZED_DEVELOPER_NAME) {
                    brandSPMap.put(authBrand.Brands__c, authBrand);
                    spMap.put(authBrand.Id, authBrand);
                }
            }
        }

        Map<String, Set<String>> spProductMap = new Map<String, Set<String>>();
        for(String brand : brandSPMap.keySet()) {
            Sales_Program__c sp = brandSPMap.get(brand);
            for(Product2 prod : products) {
                if(brand == prod.Brand_Name__c) {
                    if(!spProductMap.containsKey(sp.Id)) {
                        spProductMap.put(sp.Id, new Set<String>());
                    }
                    spProductMap.get(sp.Id).add(prod.Id);
                }
            }
        }

        // classify authorized brand use contract price list
        Map<String, Boolean> spUseContractMap = new Map<String, Boolean>();
        for(Sales_Program__c sp : brandSPMap.values()) {
            if (sp.Price_Book_Mapping__r.Name == 'Contract Price List' && String.isNotBlank(sp.Contract_Price_Book__c)) {
                spUseContractMap.put(sp.Id, true);
            }
            else {
                spUseContractMap.put(sp.Id, false);
            }
        }

        Map<String, PricebookEntry> prodEntryMap = getPriceBookEntryByProdId(spUseContractMap, spProductMap, spMap);

        Set<String> productNeedStarndAuthBrandPrice = new Set<String>();
        for(String prodId : prodIds) {
            if(!prodEntryMap.containsKey(prodId)) {
                productNeedStarndAuthBrandPrice.add(prodId);
            }
        }
        if(!productNeedStarndAuthBrandPrice.isEmpty()) {
            prodEntryMap.putAll(getStandardPricebookEntryByProdId(productNeedStarndAuthBrandPrice, spMap));
        }
        return prodEntryMap;
    }

    
    private static Map<String, PricebookEntry> getPriceBookEntryByProdId(Map<String, Boolean> spUseContractMap, Map<String, Set<String>> spProductMap, Map<String, Sales_Program__c> spMap) {
        Set<String> prod1s = new Set<String>();
        Set<String> priceBook1s = new Set<String>();

        Set<String> prod2s = new Set<String>();
        Set<String> priceBook2s = new Set<String>();

        for(String spId : spUseContractMap.keySet()) {
            if(spUseContractMap.get(spId)) {
                if(spProductMap.containsKey(spId)) {
                    prod1s.addAll(spProductMap.get(spId));
                }
                if(spMap.containsKey(spId)) {
                    priceBook1s.add(spMap.get(spId).Price_Book__c);
                }
            }
            else {
                if(spProductMap.containsKey(spId)) {
                    prod2s.addAll(spProductMap.get(spId));
                }
                if(spMap.containsKey(spId)) {
                    priceBook2s.add(spMap.get(spId).Price_Book__c);
                }
            }
        }

        Map<String, PricebookEntry> prodEntryMap = new Map<String, PricebookEntry>();
        if(!priceBook2s.isEmpty() && !prod2s.isEmpty()) {
            List<Pricebook2> priceBooks = [
                SELECT IsStandard, Name, IsActive, Id,
                    Price_Book_OracleID__c,
                    Contract_Price_Book_OracleID__c
                    FROM Pricebook2 
                    WHERE IsStandard = false
                    AND Id IN : priceBook2s
                    AND IsActive = true];
            List<PricebookEntry> priceEntries = [
                SELECT convertCurrency(UnitPrice),Product2Id,Name,Pricebook2Id 
                    FROM PricebookEntry
                    WHERE IsActive = true
                    AND IsDeleted = false
                    AND Pricebook2Id IN: priceBooks
                    AND Product2Id IN: prod2s];
            for(PricebookEntry entry : priceEntries) {
                prodEntryMap.put(entry.Product2Id, entry);
            }
        }

        if(!priceBook1s.isEmpty() && !prod1s.isEmpty()) {
            Map<String, String> contractPriceBookMap = new Map<String, String>();
            Set<String> contarctPriceIdSet = new Set<String>();
            Set<String> contracrOracleIdSet = new Set<String>();

            List<Pricebook2> priceBooks = [
                SELECT IsStandard, Name, IsActive, Id,
                    Price_Book_OracleID__c,
                    Contract_Price_Book_OracleID__c
                    FROM Pricebook2 
                    WHERE IsStandard = false
                    AND Id IN : priceBook1s
                    AND IsActive = true];
            Set<String> oracleIdSet = new Set<String>();
            for(Pricebook2 priceBook : priceBooks) {
                contractPriceBookMap.put(priceBook.Price_Book_OracleID__c, pricebook.Id);
                contarctPriceIdSet.add(pricebook.Id);
                contracrOracleIdSet.add(priceBook.Price_Book_OracleID__c);

                if (String.isNotBlank(priceBook.Contract_Price_Book_OracleID__c)) {
                    oracleIdSet.addAll(priceBook.Contract_Price_Book_OracleID__c.split(','));
                    contracrOracleIdSet.addAll(priceBook.Contract_Price_Book_OracleID__c.split(','));
                }
            }
            List<Pricebook2> contractPriceBookList = [SELECT Id, IsActive, Price_Book_OracleID__c FROM Pricebook2 WHERE Price_Book_OracleID__c IN: oracleIdSet AND IsActive = true];
            if (contractPriceBookList != null && contractPriceBookList.size() > 0){
                for (Pricebook2 pbItem : contractPriceBookList){
                    contractPriceBookMap.put(pbItem.Price_Book_OracleID__c, pbItem.Id);
                    contarctPriceIdSet.add(pbItem.Id);
                }
            }

            Map<String, List<PricebookEntry>> entryMap = new Map<String, List<PricebookEntry>>();
            Set<String> keyCompareSet = new Set<String>();
            List<PricebookEntry> priceEntries = [
                        SELECT convertCurrency(UnitPrice), Product2Id, Name, Pricebook2Id 
                            FROM PricebookEntry
                            WHERE IsActive = true
                            AND IsDeleted = false
                            AND Pricebook2Id IN: contarctPriceIdSet
                            AND Product2Id IN: prod1s];
            for (PricebookEntry entry : priceEntries){
                String key = String.valueOf(entry.Pricebook2Id) + String.valueOf(entry.Product2Id);
                if(!keyCompareSet.contains(key) && !entryMap.containsKey(entry.Pricebook2Id)) {
                    entryMap.put(entry.Pricebook2Id, new List<PricebookEntry>());
                    keyCompareSet.add(key);
                }
                entryMap.get(entry.Pricebook2Id).add(entry);
            }

            for (String orId : contracrOracleIdSet) {
                String priceBookRecId = contractPriceBookMap.get(orId);
                if(entryMap.get(priceBookRecId) != null) {
                    for(PricebookEntry entry : entryMap.get(priceBookRecId)) {
                        prodEntryMap.put(entry.Product2Id, entry);
                    }
                }
            }
        }
        return prodEntryMap;
    }


    private static Map<String, PricebookEntry> getStandardPricebookEntryByProdId(Set<String> productNeedStarndAuthBrandPrice, Map<String, Sales_Program__c> spMap) {
        Set<String> brandNames = new Set<String>();
        Set<String> customerTypes = new Set<String>();
        for(Sales_Program__c sp : spMap.values()) {
            brandNames.add(sp.Brands__c);
            customerTypes.add(sp.Customer__r.Distributor_or_Dealer__c);
        }

        Set<String> priceBookIds = new Set<String>();
        for(Customer_Brand_Pricebook_Mapping__c pricebookMap : [SELECT Id,
                                                                    Name,
                                                                    Brand__c,
                                                                    Customer_Type__c,
                                                                    Price_Book__c,
                                                                    Type__c
                                                            FROM Customer_Brand_Pricebook_Mapping__c
                                                            WHERE Brand__c = :brandNames
                                                            AND Customer_Type__c = :customerTypes
                                                            AND Authorized_Brand_Type__c = 'Standard'
                                                            AND Type__c = 'Sales'
                                                            AND Special_Dropship_Address__c != true]) {
            if(String.isNotBlank(pricebookMap.Price_Book__c)) {
                priceBookIds.add(pricebookMap.Price_Book__c);
            }
        }
        Map<String, PricebookEntry> prodEntryMap = new Map<String, PricebookEntry>();
        if(!priceBookIds.isEmpty()) {
            List<PricebookEntry> priceEntries = [
                    SELECT convertCurrency(UnitPrice), Product2Id, Name, Pricebook2Id 
                        FROM PricebookEntry
                        WHERE IsActive = true
                        AND IsDeleted = false
                        AND Pricebook2Id IN: priceBookIds
                        AND Product2Id IN: productNeedStarndAuthBrandPrice];
            for(PricebookEntry entry : priceEntries) {
                prodEntryMap.put(entry.Product2Id, entry);
            }
        }
        return prodEntryMap;
    }


    public static Map<String, PricebookEntry> getDropShipPriceBookPerProductCA(Set<String> prodIds, String customerId) {
        List<Product2> prodInfos = [SELECT Id, Brand_Name__c,Item_Number__c,ProductCode,CS_Exchange_Rate__c,Weight__c,OverSize__c FROM Product2 WHERE Id IN :prodIds];
        Set<String> brands = new Set<String>();
        for(Product2 prodInfo : prodInfos) {
            brands.add(prodInfo.Brand_Name__c);
        }

        Set<Id> serviceTypes = new Set<Id> {
            CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_ID,
            CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID,
            CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID
        };

        Boolean isSpecial = false;
        Set<String> dropShipBillingIds = new Set<String>();
        List<Sales_Program__c> authBrandList = [
                    SELECT Id, Customer__c, Brands__c,Approval_Status__c, 
                            IsDeleted, RecordType.DeveloperName,
                            (SELECT Id, Address_Type__c,Special_Dropship_Address__c, Customer_Line_Oracle_ID__c
                                FROM Addresses_With_Program__r 
                                WHERE Address_Type__c = 'Dropship Billing Address' AND Special_Dropship_Address__c = true
                                AND Customer_Line_Oracle_ID__c != null) 
                    FROM Sales_Program__c 
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c IN: brands
                    AND RecordTypeId NOT IN :serviceTypes];
        for(Sales_Program__c authBrand : authBrandList) {
            if (authBrand.Addresses_With_Program__r != null && authBrand.Addresses_With_Program__r.size() > 0){
                isSpecial = true;
                for(Address_With_Program__c awp : authBrand.Addresses_With_Program__r) {
                    dropShipBillingIds.add(awp.Id);
                }
            }
        }

        String sqlString = 'SELECT Id, Name, Brand__c, Customer_Type__c, Price_Book__c, Type__c, '
                               +'Special_Dropship_Address__c, Billing_Address_With_Authorized_Brand__c, '
                               +'Price_Book__r.Contract_Price_Book_OracleID__c, Price_Book__r.Price_Book_OracleID__c '
                               +'FROM Customer_Brand_Pricebook_Mapping__c '
                               +'WHERE Type__c = \'Dropship\' '
                               +'AND Price_Book__c != null';
        if (isSpecial){
            sqlString += ' AND Billing_Address_With_Authorized_Brand__c IN :dropShipBillingIds AND Special_Dropship_Address__c = true';
        }else{
            sqlString += ' AND Brand__c IN :brands AND Special_Dropship_Address__c = false';
        }
        sqlString += ' AND ORG_Code__c = \'CCA\' ';
        List<Customer_Brand_Pricebook_Mapping__c> bpList = Database.query(sqlString);
        
        Set<String> priceBookIds = new Set<String>();
        for(Customer_Brand_Pricebook_Mapping__c pricebookMap : bpList) {
            if(String.isNotBlank(pricebookMap.Price_Book__c)) {
                priceBookIds.add(pricebookMap.Price_Book__c);
            }
        }

        Map<String, PricebookEntry> prodEntryMap = new Map<String, PricebookEntry>();
        if(!priceBookIds.isEmpty() && !prodIds.isEmpty()) {
            Map<String, String> contractPriceBookMap = new Map<String, String>();
            Set<String> contarctPriceIdSet = new Set<String>();
            Set<String> contracrOracleIdSet = new Set<String>();

            List<Pricebook2> priceBooks = [
                SELECT IsStandard, Name, IsActive, Id,
                    Price_Book_OracleID__c,
                    Contract_Price_Book_OracleID__c
                    FROM Pricebook2 
                    WHERE IsStandard = false
                    AND Id IN : priceBookIds
                    AND IsActive = true];
            Set<String> oracleIdSet = new Set<String>();
            for(Pricebook2 priceBook : priceBooks) {
                contractPriceBookMap.put(priceBook.Price_Book_OracleID__c, pricebook.Id);
                contarctPriceIdSet.add(pricebook.Id);
                contracrOracleIdSet.add(priceBook.Price_Book_OracleID__c);

                if (String.isNotBlank(priceBook.Contract_Price_Book_OracleID__c)) {
                    oracleIdSet.addAll(priceBook.Contract_Price_Book_OracleID__c.split(','));
                    contracrOracleIdSet.addAll(priceBook.Contract_Price_Book_OracleID__c.split(','));
                }
            }
            List<Pricebook2> contractPriceBookList = [SELECT Id, IsActive, Price_Book_OracleID__c FROM Pricebook2 WHERE Price_Book_OracleID__c IN: oracleIdSet AND IsActive = true];
            if (contractPriceBookList != null && contractPriceBookList.size() > 0){
                for (Pricebook2 pbItem : contractPriceBookList){
                    contractPriceBookMap.put(pbItem.Price_Book_OracleID__c, pbItem.Id);
                    contarctPriceIdSet.add(pbItem.Id);
                }
            }

            Map<String, PricebookEntry> entryMap = new Map<String, PricebookEntry>();
            List<PricebookEntry> priceEntries = [
                        SELECT convertCurrency(UnitPrice), Product2Id, Name, Pricebook2Id 
                            FROM PricebookEntry
                            WHERE IsActive = true
                            AND IsDeleted = false
                            AND Pricebook2Id IN: contarctPriceIdSet
                            AND Product2Id IN: prodIds];
            for (PricebookEntry entry : priceEntries){
                entryMap.put(entry.Pricebook2Id, entry);
            }

            for (String orId : contracrOracleIdSet) {
                String priceBookRecId = contractPriceBookMap.get(orId);
                if(entryMap.get(priceBookRecId) != null) {
                    PricebookEntry entry = entryMap.get(priceBookRecId);
                    prodEntryMap.put(entry.Product2Id, entry);
                }
            }
        }
        return prodEntryMap;
    }


    public class POItemInfo {
        // input params
        public String ProductCode {get;set;}
        public Integer Quantity {get;set;}

        // convert params
        public String Brand {get;set;}
        public Decimal UnitPrice {get;set;}
        public String ProductId {get;set;}
        public String PartsId {get;set;}
        public Decimal SubTotal {get;set;}
    }


    public class ProductInfoWrapper {
        public String ProductId {get;set;}
        public String Name {get;set;}
        public String ProductCode {get;set;}
        public Integer Quantity {get;set;}
        public Decimal UnitPrice {get;set;}
        public Decimal SubTotal {get;set;}
        public CCM_Quotation_ProductSelectCtl.InitData InitData {get;set;}
    }

    public class ReturnMessage {
        public Boolean IsSuccess {get;set;}
        public String ErrorMessage {get;set;}
    }
}