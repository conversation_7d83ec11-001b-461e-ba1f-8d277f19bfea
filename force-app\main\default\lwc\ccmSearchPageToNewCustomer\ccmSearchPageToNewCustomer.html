<template>
    <div>
        <lightning-input type="text"
            id="customerName"
            class="slds-input"
            value={customerName}
            required="true"
            onchange={handleCustomerNameChange}
            label="Customer Name">
        </lightning-input>
        <lightning-input type="text"
            id="recordType"
            class="slds-input"
            disabled={recordTypeDisabled}
            value={recordType}
            onchange={handleRecordTypeChange}
            label="Record Type">
        </lightning-input>
        <lightning-input type="text"
            id="website"
            class="slds-input"
            value={website}
            onchange={handleWebsiteChange}
            label="Website">
        </lightning-input>
        <lightning-input-address
            id="shippingAddress"
            class="slds-input"
            address-label="shipping Address"
            street-label="Street"
            city-label="City"
            country-label="Country"
            province-label="Province"
            postal-code-label="PostalCode"
            required
            onchange={handleShippingAddressChange}>
        </lightning-input-address>
    </div>
</template>