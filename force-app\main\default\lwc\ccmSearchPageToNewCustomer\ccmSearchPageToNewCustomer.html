<template>
    <div class="slds-card">
        <!-- Header -->
        <div class="slds-card__header slds-grid">
            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                <div class="slds-media__body">
                    <h2 class="slds-card__header-title slds-grid">
                        <span class="slds-text-heading_medium slds-grid_align-center">New Account</span>
                    </h2>
                </div>
            </header>
        </div>

        <!-- Card Body -->
        <div class="slds-card__body slds-card__body_inner">
            <!-- Loading Spinner -->
            <template if:true={isProcessing}>
                <div class="slds-spinner_container">
                    <div role="status" class="slds-spinner slds-spinner_medium">
                        <span class="slds-assistive-text">Loading</span>
                        <div class="slds-spinner__dot-a"></div>
                        <div class="slds-spinner__dot-b"></div>
                    </div>
                </div>
            </template>

            <!-- Account Information Section -->
            <div class="slds-section slds-is-open">
                <h3 class="slds-section__title slds-theme_shade">
                    <span class="slds-truncate slds-p-horizontal_small" title="Account Information">
                        Account Information
                    </span>
                </h3>
                <div class="slds-section__content">
                    <div class="slds-p-around_medium">
                        <div class="slds-grid slds-gutters slds-wrap">
                            <!-- Customer Name -->
                            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                                <lightning-input
                                    id="customerName"
                                    type="text"
                                    label="Customer Name"
                                    value={customerName}
                                    onchange={handleCustomerNameChange}
                                    required
                                    class="custom-input">
                                </lightning-input>
                            </div>

                            <!-- Record Type -->
                            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                                <lightning-combobox
                                    id="recordType"
                                    label="Record Type"
                                    value={recordType}
                                    options={recordTypeOptions}
                                    onchange={handleRecordTypeChange}
                                    required
                                    class="custom-input">
                                </lightning-combobox>
                            </div>

                            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                                <lightning-input-address
                                    id="shippingAddress"
                                    address-label="Shipping Address"
                                    street-label="Street"
                                    city-label="City"
                                    country-label="Country"
                                    province-label="State/Province"
                                    postal-code-label="Postal Code"
                                    onchange={handleShippingAddressChange}
                                    class="custom-address">
                                </lightning-input-address>
                            </div>
                            
                            <!-- Website -->
                            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                                <lightning-input
                                    id="website"
                                    type="url"
                                    label="Website"
                                    value={website}
                                    required
                                    onchange={handleWebsiteChange}
                                    class="custom-input">
                                </lightning-input>
                            </div>
                        </div>
                    </div>
                </div>
            </div>    
        </div>

        <!-- Card Footer with Action Buttons -->
        <div class="slds-grid slds-gutters slds-p-around_medium">
            <div class="slds-col slds-size_1-of-1 slds-text-align_center">
                <button class="slds-button slds-button_neutral slds-m-right_small" onclick={handleCancel}>
                    Cancel
                </button>
                <button class="slds-button slds-button_brand" onclick={handleSave} disabled={isProcessing}>
                    Save
                </button>
            </div>
        </div>
    </div>
</template>