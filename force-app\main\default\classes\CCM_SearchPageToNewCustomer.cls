public with sharing class CCM_SearchPageToNewCustomer {

    public class AccountParams {
        @AuraEnabled public String customerName{get;set;}
        @AuraEnabled public String recordType{get;set;}
        @AuraEnabled public String website{get;set;}
        @AuraEnabled public String shippingStreet{get;set;}
        @AuraEnabled public String shippingCity{get;set;}
        @AuraEnabled public String shippingState{get;set;}
        @AuraEnabled public String shippingPostalCode{get;set;}
        @AuraEnabled public String shippingCountry{get;set;}
    }
    
    public class CreateResult {
        @AuraEnabled public Boolean success;
        @AuraEnabled public String message;
        @AuraEnabled public String accountId;
    }
    
    @AuraEnabled(cacheable=true)
    public static List<Map<String, String>> getAccountRecordTypes() {
        List<Map<String, String>> recordTypeOptions = new List<Map<String, String>>();
        
        try {
            List<RecordType> recordTypes = [
                SELECT Id, Name, DeveloperName 
                FROM RecordType 
                WHERE SObjectType = 'Account' 
                AND IsActive = true 
                ORDER BY Name
            ];
            
            for (RecordType rt : recordTypes) {
                Map<String, String> option = new Map<String, String>();
                option.put('label', rt.Name);
                option.put('value', rt.Id);
                recordTypeOptions.add(option);
            }
        } catch (Exception e) {
            System.debug('Error fetching record types: ' + e.getMessage());
        }
        
        return recordTypeOptions;
    }
    
    @AuraEnabled
    public static CreateResult createAccount(AccountParams params) {
        CreateResult result = new CreateResult();
        Savepoint sp = Database.setSavepoint();
        
        try {
            if (String.isBlank(params.customerName)) {
                result.success = false;
                result.message = 'Customer Name is required';
                return result;
            }
            
            if (String.isBlank(params.recordType)) {
                result.success = false;
                result.message = 'Record Type is required';
                return result;
            }
            
            // Create new Account
            Account newAccount = new Account();
            newAccount.Name = params.customerName;
            newAccount.RecordTypeId = params.recordType;
            
            // Set website if provided
            if (String.isNotBlank(params.website)) {
                newAccount.Website = params.website;
            }
            
            // Set shipping address if provided
            if (String.isNotBlank(params.shippingStreet)) {
                newAccount.ShippingStreet = params.shippingStreet;
            }
            if (String.isNotBlank(params.shippingCity)) {
                newAccount.ShippingCity = params.shippingCity;
            }
            if (String.isNotBlank(params.shippingState)) {
                newAccount.ShippingState = params.shippingState;
            }
            if (String.isNotBlank(params.shippingPostalCode)) {
                newAccount.ShippingPostalCode = params.shippingPostalCode;
            }
            if (String.isNotBlank(params.shippingCountry)) {
                newAccount.ShippingCountry = params.shippingCountry;
            }
            
            insert newAccount;
            
            result.success = true;
            result.message = 'Account created successfully';
            result.accountId = newAccount.Id;
        } catch (Exception e) {
            Database.rollback(sp);
            result.success = false;
            result.message = 'error occurred: ' + e.getMessage() + ' at line ' + e.getLineNumber();
        }
        return result;
    }
    
    // @AuraEnabled
    // public static Boolean validateWebsite(String website) {
    //     if (String.isBlank(website)) {
    //         return true; // Empty is valid (optional field)
    //     }
        
    //     // Basic URL validation
    //     String urlPattern = '^https?://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(/.*)?$';
    //     Pattern p = Pattern.compile(urlPattern);
    //     Matcher m = p.matcher(website);
        
    //     return m.matches();
    // }
}