Warranty：
	customer
		; --Account的email会赋值给customerEmail // no
		; --WarrantyItemTriggerHandle：AccountCustomer更改之后同步给heroku等。 
		; --CCM_Warranty_RegistrationEmailUtil：通知邮件时限定了recordType。//no
		; --IoTUtils.syncWarrantyItemToHeroku（有没有弃用）  // commerical user类型不限制
		; --Account_SyncToHeroku： //y
		; --CCM_SearchWarrantyTool：lastName和PersonEmail。//no
		; --CCM_SearchWarrantyTool_US //no
		; --CCM_EuropeanWarrantyExpirationHandler: 设置Site_Origin__c //赋值account
		
		--CCM_ProductLookUpController：case时根据contact查询warranty，限定了recordtype//y
		; --CCM_NewClaim：customer用到phone等
		; --CCM_ProductRegistration: 他的customer只能选end user account //delear portal
		; --UpdateWarrantyToCustomer: 涉及到customer的sku //n
		; --ProductWarranty：
		; --CCM_WarrantyItemIOTHandler：customer的site origin
		; --CCM_Warranty_RegistrationEmailUtil
		; --CCM_Warranty_SyncToHerokuUtil：ego password
		; --CCM_Warranty_UpdateExpirationDateUtil 
		; --CCM_WarrantyPackToErpBatch：org code //n
		; --CCM_ProductCustomerController：Site_Origin
		--CreateProjectCustomerBS：recall message限定了End User Account。已关闭的recall类型的case不会生成recall message。//yes                                                                                                                                           
Case: End User Account
	; Customer： Geolocalization:手机上的定位
	; PersonContactId：邮件用到了
	; ContactEmail,ContactFax,ContactMobile,ContactPhone有数据
	; CCM_ProductLookUpController:CaseWarrantyNumber限定recordtype。CaseGeoContact，经纬度。
	CCM_CaseController：case时，默认选中customer的第一个contact //y



Order
	; CustomerEmail__c,Custome_Phone__c用的account的PersonEmail，phone。
	AccountId：case不空时case的AccountId，warranty不空时warranty的AccountCustomer。
	customer__c：case的Contact。
	; address。
	; billTo相关信息：case的account。
	; --CCM_NewOrderController：下单时用到了account的email，phone
