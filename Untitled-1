Warranty：
	1 CustomerEmail（弃用了？）
	2 customer
		--AccountCustomer更改之后同步给heroku等。  
		--CCM_Warranty_RegistrationEmailUtil：通知邮件时限定了recordType。
		--IoTUtils.syncWarrantyItemToHeroku（有没有弃用）  
		--CCM_SearchWarrantyTool：lastName和PersonEmail。
		--CCM_SearchWarrantyTool_US 
		--CCM_EuropeanWarrantyExpirationHandler: 设置Site_Origin__c
		--CCM_CaseController：case时，默认选中customer的第一个contact
		--CCM_NewOrderController：下单时用到了account的email，phone
		--CCM_ProductLookUpController：case时根据contact查询warranty，限定了recordtype
		--CCM_NewClaim：customer用到phone等
		--CCM_ProductRegistration: 他的customer只能选end user account
		--UpdateWarrantyToCustomer: 涉及到customer的sku
		--ProductWarranty：
		--CCM_WarrantyItemIOTHandler：customer的site origin
		--CCM_Warranty_RegistrationEmailUtil
		--CCM_Warranty_SyncToHerokuUtil：ego password
		--CCM_Warranty_UpdateExpirationDateUtil 
		--CCM_WarrantyPackToErpBatch：org code
		--CCM_ProductCustomerController：Site_Origin
		--
		                                                                                                                                            
Case:
