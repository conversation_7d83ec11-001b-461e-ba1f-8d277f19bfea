/**
 * <AUTHOR>
 * @date 2023-06-27
 * @description Create purchase order from input information
 */
public without sharing class CCM_PurchaseOrderUpload {

    public static List<CCM_PartsOrder_CheckLogic.ReturnMessage> errorMessages = new List<CCM_PartsOrder_CheckLogic.ReturnMessage>();

    public static List<CCM_PartsOrder_CheckLogic.ErrorDetail> errorDetails = new List<CCM_PartsOrder_CheckLogic.ErrorDetail>();

    private static Map<String, String> orgCurrencyMap = new Map<String, String> {
        'CNA' => 'USD',
        'CCA' => 'CAD'
    };

    public static Purchase_Order__c createPO(POInfo poInfo, Purchase_Order__c po, Boolean isParts) {
        System.debug('poInfo:' + poInfo);
        // Get information related to customers
        Account customer = getCustomerInfo(poInfo.CustomerNumber);
        if(customer == null) {
            CCM_PartsOrder_CheckLogic.ReturnMessage errorMessage = new CCM_PartsOrder_CheckLogic.ReturnMessage();
            errorMessage.IsSuccess = false;
            errorMessage.ErrorMessage = String.format('Account Number {0} not Exist, Please Correct.', new List<String>{poInfo.CustomerNumber});
            errorMessages.add(errorMessage);   
            return null;
        }
        CCM_PartsOrder_CheckLogic.ReturnMessage errorMessage =  CCM_PartsOrder_CheckLogic.checkAccountEligible(customer);
        if(errorMessage != null) {
            errorMessages.add(errorMessage);
            return null;
        }

        poInfo.CustomerId = customer.Id;
        poInfo.OrgCode = customer.ORG_Code__c;
        poInfo.ShipPriority = customer.Shipment_Priority__c;

        Set<String> allProductCodes = new Set<String>();
        Map<String, Product2> productMap = getProductMap(poInfo.POItems, allProductCodes);
        errorMessage =  CCM_PartsOrder_CheckLogic.checkProductExist(allProductCodes, productMap.keySet());
        if(errorMessage != null) {
            errorMessages.add(errorMessage);
            return null;
        }


        List<String> brands = gatherBrands(productMap);

        // Fill in purchase order item information
        List<Purchase_Order_Item__c> poItems = generatePOItems(poInfo.POItems, poInfo, productMap);

        // Fill in purchase order information
        Purchase_Order__c newPO = generatePO(poInfo, po, brands, isParts);


        if(String.isNotBlank(po.Id)) {
            newPO.Id = po.Id;
            deleteExistPOItems(po.Id);
        }
        Database.upsert(newPO);

        for(Purchase_Order_Item__c poItem : poItems) {
            poItem.Purchase_Order__c = newPO.Id;
        }
        Database.insert(poItems);
        
        return po;
    }


    public static Purchase_Order__c generatePO(POInfo poInfo, Purchase_Order__c po, List<String> brands, Boolean isParts) {
        setBasicInfo(poInfo, po, brands, isParts);
        findBillingTo(poInfo, po, brands);
        findShippingTo(poInfo, po);
        setFreightAndPayment(poInfo, po);
        calculateTax(poInfo, po);
        return po;
    }

    public static void setBasicInfo(POInfo poInfo, Purchase_Order__c po, List<String> brands, Boolean isParts) {
        po.Customer__c = poInfo.CustomerId;

        Map<String, String> shipByMap = Util.getPicklistLabelValueMap('Purchase_Order__c', 'Shipping_By__c');
        po.Shipping_By__c = shipByMap.get(poInfo.ShipBy);
        po.Shipping_Method__c = 'Ground Freight';
        po.Customer_PO_Num__c = poInfo.CustomerPurchaseOrder;
        po.Email__c = poInfo.BuyerEmail;
        po.Notes__c = poInfo.ShippingAndRoutingInstrcuction;
        po.Brand_Scope__c = String.join(brands, '&');
        if('CNA' == poInfo.OrgCode) {
            po.Order_Type__c    = CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CNA_PARTS_SALES_ORDER_USD;
            po.CurrencyIsoCode  = 'USD';
        }
        else if('CCA' == poInfo.OrgCode) {
            po.Order_Type__c    = CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CCA_PARTS_SALES_ORDER_CAD;
            po.CurrencyIsoCode  = 'CAD';
        }
        po.CurrencyIsoCode = orgCurrencyMap.get(poInfo.OrgCode);
        po.Shipping_priority__c = poInfo.ShipPriority;
        po.ORG_ID__c = poInfo.OrgCode;
        if(isParts) {
            po.RecordTypeId = CCM_Constants.PURCHASE_ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID;
        }
        else {
            po.RecordTypeId = CCM_Constants.PURCHASE_ORDER_RECORD_TYPE_PLACE_ORDER_ID;
        }
        if(poInfo.OrgCode == CCM_Constants.ORG_CODE_CCA) {
            po.Expected_Delivery_Date__c = Date.today();
        }
        else {
            po.Expected_Delivery_Date__c = CCM_PartsOrder_DetailCtl.getMinSelectDate(Date.today());
        }
        po.Submit_Date__c = Date.today();
    }

    // Find billing to
    public static void findBillingTo(POInfo poInfo, Purchase_Order__c po, List<String> brands) {
        if(String.isBlank(poInfo.BillingAddress)) {
            return;
        }
        List<Account_Address__c> addressList = [SELECT Id FROM Account_Address__c WHERE Name = :poInfo.BillingAddress AND Customer__c = :poInfo.customerId];
        if(!addressList.isEmpty()) {
            poInfo.BillingAddressId = addressList[0].Id;
        }

        if(String.isBlank(poInfo.BillingAddressId)) {
            CCM_PartsOrder_CheckLogic.ReturnMessage errorMessage = new CCM_PartsOrder_CheckLogic.ReturnMessage();
            errorMessage.IsSuccess = false;
            errorMessage.ErrorMessage = String.format('Billing Address {0} not Exist, Please Correct.', new List<String>{poInfo.BillingAddress});
            errorMessages.add(errorMessage);
            return;
        }

        Id idParent;
        if (String.isNotBlank(poInfo.CustomerId)) {
            for (Account_Address__c objAA : [
                SELECT Customer__c
                FROM Account_Address__c
                WHERE X2nd_Tier_Dealer__c = :poInfo.CustomerId
                AND (RecordType_Name__c = 'Shipping_Address' OR RecordType_Name__c = 'Dropship_Shipping_Address')
                AND Approval_Status__c = 'Approved'
                AND Active__c = TRUE
                LIMIT 1
            ]) {
                idParent = objAA.Customer__c;
            }
        }

        String brandsItems = String.join(brands,'&');
        System.debug('poInfo.BillingAddressId:' + poInfo.BillingAddressId);
        System.debug('poInfo.CustomerId:' + poInfo.CustomerId);
        System.debug('brandsItems:' + brandsItems);
        Util.AddressOracleInfo bInfo = Util.getPartsOrderAddressOracelId(poInfo.BillingAddressId, 
                                        String.isBlank(idParent) ? poInfo.CustomerId : idParent, 
                                        brandsItems, true);
        System.debug(bInfo.sfId);
        po.BillTo_OracleID__c = bInfo.oracelId;
        if (String.isNotBlank(bInfo.sfId)){
            po.BillTo__c = bInfo.sfId;
        }

        List<Address_With_Program__c> adds =[ SELECT Id,Account_Address__c FROM Address_With_Program__c WHERE Id =: po.BillTo__c LIMIT 1];  
        if (adds.size() > 0) {
            po.Billing_Address__c = adds[0].Account_Address__c;
        }
    }

    // Find ship to
    public static void findShippingTo(POInfo poInfo, Purchase_Order__c po) {
        if(String.isBlank(poInfo.ShippingAddress)) {
            return;
        }
        System.debug('*** poInfo.ShippingAddress:' + poInfo.ShippingAddress);
        System.debug('*** poInfo.customerId:' + poInfo.customerId);
        List<Account_Address__c> addressList = [SELECT Id FROM Account_Address__c WHERE Name = :poInfo.ShippingAddress AND Customer__c = :poInfo.customerId];
        if(!addressList.isEmpty()){
            poInfo.ShippingAddressId = addressList[0].Id;
        }

        if(String.isBlank(poInfo.ShippingAddressId)) {
            CCM_PartsOrder_CheckLogic.ReturnMessage errorMessage = new CCM_PartsOrder_CheckLogic.ReturnMessage();
            errorMessage.IsSuccess = false;
            errorMessage.ErrorMessage = String.format('Shipping Address {0} not Exist, Please Correct.', new List<String>{poInfo.ShippingAddress});
            errorMessages.add(errorMessage);
            return;
        }

        if(!poInfo.IsAlternativeAddress) {
            Util.AddressOracleInfo sInfo = Util.getPartsOrderAddressOracelId(poInfo.ShippingAddressId, 
                                    poInfo.CustomerId, null, false);

            po.ShipTo_OracleID__c = sInfo.oracelId;
            if (String.isNotBlank(sInfo.sfId)){
                po.ShipTo__c = sInfo.sfId;
            }

            Schema.DescribeSObjectResult r = Account_Address__c.sObjectType.getDescribe();
            List<String> AddressApiNames =  new List<String>();
            for(string apiName : r.fields.getMap().keySet()){
                AddressApiNames.add(apiName);
            }
            AddressApiNames.add('Contact__r.Name');
            AddressApiNames.add('Contact__r.Phone');
            String queryStr = 'SELECT ' + String.join(AddressApiNames, ', ') + ' FROM Account_Address__c WHERE Id = \'' + poInfo.ShippingAddressId + '\'' ;
            Account_Address__c add = Database.query(queryStr);
            
            po.Additional_Shipping_Street__c = add.Address1__c;
            po.Additional_Shipping_Street2__c = add.Address2__c;
            po.Additional_Contact_Phone__c = add.Contact__r.Phone;
            po.Additional_Contact_Name__c = add.Contact__r.Name;
            po.Additional_Shipping_Country__c = add.Country__c;
            po.Additional_Shipping_Postal_Code__c = add.Postal_Code__c;
            po.Additional_Shipping_Province__c = add.State__c;
            po.Additional_Shipping_City__c = add.City__c;
        }
        else {
            for (Account_Address__c objAA : [
                SELECT (SELECT Customer_Line_Oracle_ID__c FROM Addresses_With_Program__r LIMIT 1)
                FROM Account_Address__c
                WHERE
                    ((X2nd_Tier_Dealer__c = :poInfo.CustomerId
                    AND X2nd_Tier_Dealer__r.Distributor_or_Dealer__c = '2nd Tier Dealer')
                    OR (Customer__c = :poInfo.CustomerId
                    AND Customer__r.Distributor_or_Dealer__c != '2nd Tier Dealer'))
                    AND (RecordType_Name__c = 'Shipping_Address'
                    OR RecordType_Name__c = 'Dropship_Shipping_Address')
                    AND Approval_Status__c = 'Approved'
                    AND Active__c = TRUE
                LIMIT 1
            ]) {
                for (Address_With_Program__c objAP : objAA.Addresses_With_Program__r) {
                    po.ShipTo_OracleID__c = objAP.Customer_Line_Oracle_ID__c;
                    po.ShipTo__c = objAP.Id;
                }
            }
        }

        List<Address_With_Program__c> adds =[ SELECT Id,Account_Address__c FROM Address_With_Program__c WHERE Id =: po.ShipTo__c LIMIT 1];  
        if (adds.size() > 0) {
            po.Shipping_Address__c = adds[0].Account_Address__c;
        }
    }

    // Set Freight and Payment
    public static void setFreightAndPayment(POInfo poInfo, Purchase_Order__c po) {
        Map<String, String> paymentAndFreightTermMap = getPaymentFreightTermByCustomerId(poInfo.CustomerId, poInfo.OrgCode);

        System_Configuration__c sc = new System_Configuration__c();
        if('CNA' == poInfo.OrgCode && poInfo.ProductPrice > 100 ) {
            sc.Freight_Charge__c        = 20.00;
            sc.Freight_Fee_Waived__c    = 20.00;
        } else {
            sc = Util.getFreightAndWaviedFee(poInfo.OrgCode, poInfo.ProductPrice);  
        }
        System.debug(sc);
        if (sc != null && poInfo.ShipBy != 'Customer') {
            po.Freight_Fee__c                 = sc.Freight_Charge__c          == null ? 0 : sc.Freight_Charge__c;
            po.Freight_Fee_Waived__c          = sc.Freight_Fee_Waived__c      == null ? 0 : sc.Freight_Fee_Waived__c;
            po.Freight_Fee_To_Be_Waived__c    = sc.Freight_Fee_Waived__c      == null ? 0 : sc.Freight_Fee_Waived__c;
        } else if (poInfo.ShipBy == 'Customer'){
            po.Freight_Fee__c               = 0.00;
            po.Freight_Fee_Waived__c        = 0.00;
            po.Freight_Fee_To_Be_Waived__c  = 0.00;
        }

        if (poInfo.OrgCode == 'CCA') {
            if (po.Shipping_By__c != 'Customer') {
                po.Freight_Term__c = String.isNotBlank(paymentAndFreightTermMap.get('freightTerm'))? paymentAndFreightTermMap.get('freightTerm') : 'PPB';
            }
            po.Payment_Term__c = String.isNotBlank(paymentAndFreightTermMap.get('paymentTerm'))? paymentAndFreightTermMap.get('paymentTerm') : 'CA001';

        } else if (poInfo.OrgCode == 'CNA') {
            if (po.Shipping_By__c != 'Customer') {
                po.Freight_Term__c = String.isNotBlank(paymentAndFreightTermMap.get('freightTerm'))? paymentAndFreightTermMap.get('freightTerm') : 'Paid';
            }
            po.Payment_Term__c = String.isNotBlank(paymentAndFreightTermMap.get('paymentTerm'))? paymentAndFreightTermMap.get('paymentTerm') : 'NA001';
        }
    }

    // Calculate Tax
    public static void calculateTax(POInfo poInfo, Purchase_Order__c po) {
        po.Product_Price__c = poInfo.ProductPrice;
        po.Total_Quantity__c = poInfo.TotalQuantity;
        if(String.isNotBlank(po.ShipTo__c) && 'CCA' == poInfo.OrgCode) {
            Util.caculateCcaPartsOrderTotalTax(po);
        }
    }

    // Get Freight&Payment Term from Authorized Brand
    public static Map<String, String> getPaymentFreightTermByCustomerId(String customerId, String orgCode) {
        Map<String, String> paymentAndFreightTermMap = new Map<String, String>();
        if (String.isNotBlank(customerId)){
            Map<String, Freight_Term__mdt> freightRuleMap = Util.getFreightTermMap();
            List<Sales_Program__c> authBrands = new List<Sales_Program__c>();
            if (orgCode == 'CNA') {
                authBrands = Util.getCnaSerivceAuthBrandInfo(customerId);
            } else if (orgCode == 'CCA') {
                authBrands = Util.getCcaSerivceAuthBrandInfo(customerId);
            }
            if (authBrands != null && authBrands.size() > 0){
                Sales_Program__c authBrand = authBrands[0];
                paymentAndFreightTermMap.put('paymentTerm', authBrand.Payment_Term__c);
                paymentAndFreightTermMap.put('freightTerm', authBrand.Freight_Term__c);
                paymentAndFreightTermMap.put('freightTermLabel', (String)authBrand.get('freightTermLabel'));
                paymentAndFreightTermMap.put('paymentTermLabel', (String)authBrand.get('paymentTermLabel')); 
                if (String.isNotBlank(authBrand.Freight_Term__c)){
                    Freight_Term__mdt fterm = freightRuleMap.get(authBrand.Freight_Term__c);
                    paymentAndFreightTermMap.put('freightTermRuleFee', String.valueOf(fterm.Freight_Fee__c));
                }
            }
        }
        return paymentAndFreightTermMap;
    }

    public static List<Purchase_Order_Item__c> generatePOItems(List<POItemInfo> poItemInfos, POInfo poInfo, Map<String, Product2> productMap) {
        Id priceBookId = findPriceBookForPOItem(poInfo.CustomerId);
        
        Decimal productPrice = 0;
        Integer totalQuantity = 0;
        List<Purchase_Order_Item__c> poItems = new List<Purchase_Order_Item__c>();

        Set<String> prodIds = new Set<String>();
        for(POItemInfo poItemInfo : poItemInfos) {
            Product2 product = productMap.get(poItemInfo.productCode);
            if(product != null) {
                prodIds.add(product.Id);
            }
        }

        Map<String, PricebookEntry> prodEntryMap = getPriceBookEntrys(prodIds, priceBookId);

        for(POItemInfo poItemInfo : poItemInfos) {

            Product2 product = productMap.get(poItemInfo.productCode);
            if(product == null) {
                CCM_PartsOrder_CheckLogic.ReturnMessage errorMessage = new CCM_PartsOrder_CheckLogic.ReturnMessage();
                errorMessage.IsSuccess = false;
                errorMessage.ErrorMessage = String.format('Parts Stock Number {0} not Exist, Please Correct.', new List<String>{poItemInfo.ProductCode});
                errorMessages.add(errorMessage);   
                continue;
            }
            PricebookEntry priceBookEntry = prodEntryMap.get(product.Id);
            if(priceBookEntry != null) {
                poItemInfo.UnitPrice = PricebookEntry.UnitPrice;
                poItemInfo.Brand = product.Brand_Name__c;
                poItemInfo.ProductId = product.Id;
                poItemInfo.SubTotal = (poItemInfo.UnitPrice * poItemInfo.Quantity).setScale(2);
                productPrice += poItemInfo.SubTotal;
                totalQuantity += poItemInfo.Quantity;

                Purchase_Order_Item__c poItem = fillinPurchaseOrderItem(poItemInfo, poInfo, priceBookId);
                poItems.add(poItem);
            }
            
        }
        poInfo.ProductPrice = productPrice;
        poInfo.TotalQuantity = totalQuantity;
        return poItems;
    }

    private static Purchase_Order_Item__c fillinPurchaseOrderItem(POItemInfo poItemInfo, POInfo poInfo, Id priceBookId) {
        Purchase_Order_Item__c poItem = new Purchase_Order_Item__c();
        // poItem.Purchase_Order__c = po.Id;
        poItem.Price_Book__c = priceBookId;
        poItem.Brand__c = poItemInfo.Brand;

        if(poInfo.OrgCode == CCM_Constants.ORG_CODE_CCA) {
            poItem.Ship_Date__c = Date.today();
        }
        else {
            poItem.Ship_Date__c = CCM_PartsOrder_DetailCtl.getMinSelectDate(Date.today());
        }

        poItem.Brand__c = poItemInfo.Brand;
        if(String.isNotBlank(poItemInfo.PartsId)){
            poItem.Product__c = poItemInfo.PartsId;
            poItem.ProductCode__c = poItemInfo.PartsCode;
        }else{
            poItem.Product__c = poItemInfo.ProductId;
            poItem.ProductCode__c = poItemInfo.ProductCode;
        }

        poItem.Quantity__c = poItemInfo.Quantity;
        poItem.Unit_Price__c = poItemInfo.UnitPrice;
        poItem.Sub_Total__c = poItemInfo.SubTotal;
        poItem.CurrencyIsoCode = orgCurrencyMap.get(poInfo.OrgCode);
        
        
        if('CCA' == poInfo.OrgCode) {
            poItem.Line_Type__c = 'CA Interco Line';
        }

        return poItem;
    }

    public static Id findPriceBookForPOItem(String customerId) {
        System.debug('*** customerId:' + customerId);
        Id priceBookId = null;
        List<Account> customers = [SELECT ORG_Code__c, Distributor_or_Dealer__c, AccountNumber FROM Account WHERE Id = :customerId];
        if(customers.isEmpty()) {
            return priceBookId;
        }

        Account customer = customers[0];
        if('CCA' == customer.ORG_Code__c) {
            priceBookId =  [
                SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c 
                WHERE Id != null
                    AND ORG_Code__c = 'CCA'
                    AND Type__c = 'Service'
                    AND Customer_Type__c =: customer.Distributor_or_Dealer__c
                    AND Is_Active__c = true
                LIMIT 1
            ].Price_Book__c;
        }
        else {
            if(customer.Distributor_or_Dealer__c.contains('Dealer') || customer.Distributor_or_Dealer__c.contains('Service Center')){
                priceBookId = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' LIMIT 1].Price_Book__c;
            }else if(customer.Distributor_or_Dealer__c.contains('Distributor')){
                priceBookId = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts' LIMIT 1].Price_Book__c;
            }
        }

        if(customer.AccountNumber == 'B10127'){
            priceBookId =  [SELECT Id FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Id;
        }
        return priceBookId;
    }

    private static Product2 findPartsByProductCode(String productCode) {
        List<Product2> parts = [SELECT ProductCode, Name, Brand_Name__c FROM Product2 WHERE IsActive = true AND Source__c = 'PIM' AND RecordType.Name = 'Parts' AND Sellable__c = true AND ProductCode = :productCode
                            AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
        if(!parts.isEmpty()) {
            return parts[0];
        }
        return null;
    }

    private static PricebookEntry getPriceBookEntry(String prodId, String priceBookId) {
        List<PricebookEntry> entrys = Util.getPriceBookEntryByProdIdWithOutConvertCurrency(prodId, priceBookId);
        if(!entrys.isEmpty()) {
            return entrys[0];
        }
        return null;
    }

    public static Map<String, PricebookEntry> getPriceBookEntrys(Set<String> prodIds, String priceBookId) {
        Map<String, PricebookEntry> prodEntryMap = getPriceBookEntryByProdIdWithOutConvertCurrency(prodIds, priceBookId);
        return prodEntryMap;
    }

    public static Account getCustomerInfo(String customerNumber) {
        Boolean isInnerUser = Util.isInnerUser();
        if(isInnerUser) {
            List<Account> customers = [SELECT Id, Name, AccountNUmber, ORG_Code__c, Shipment_Priority__c FROM Account WHERE RecordType.DeveloperName = 'Channel' AND Distributor_or_Dealer__c != null AND AccountNumber = :customerNumber];
            if(!customers.isEmpty()) {
                return customers[0];
            }
        }
        else {
            User u = Util.getUserInfo(UserInfo.getUserId());
            List<Contact> contacts = [SELECT AccountId FROM Contact WHERE Id = :u.ContactId];
            if(!contacts.isEmpty()) {
                List<Account> customers = [SELECT Id, Name, AccountNUmber, ORG_Code__c, Shipment_Priority__c FROM Account WHERE RecordType.DeveloperName = 'Channel' AND Distributor_or_Dealer__c != null AND Id = :contacts[0].AccountId];
                if(!customers.isEmpty()) {
                    return customers[0];
                }
            }
        }
        
        return null;
    }

    private static void deleteExistPOItems(String poId) {
        List<Purchase_Order_Item__c> poItems = [SELECT Id FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :poId];
        Database.delete(poItems);
    }

    public static Map<String, Product2> getProductMap(List<POItemInfo> poItemInfos, Set<String> allProductCodes) {
        Set<String> productCodes = new Set<String>();
        for(POItemInfo itemInfo : poItemInfos) {
            if(String.isNotBlank(itemInfo.ProductCode)) {
                productCodes.add(itemInfo.ProductCode);
                allProductCodes.add(itemInfo.ProductCode);
            }
        }
        
        Map<String, Product2> productMap = new Map<String, Product2>();
        for(Product2 prod : [SELECT ProductCode, Brand_Name__c, Item_Number__c, Name FROM Product2 WHERE IsActive = true AND Source__c = 'PIM' AND RecordType.Name = 'Parts' AND Sellable__c = true AND ProductCode = :productCodes 
                            AND Is_History_Product__c = :CCM_Constants.blHistoryProduct]) {
            productMap.put(prod.ProductCode, prod);
        }
        return productMap;
    }

    private static List<String> gatherBrands(Map<String, Product2> productMap) {
        Set<String> brandSet = new Set<String>();
        for(Product2 prod : productMap.values()) {
            if(String.isNotBlank(prod.Brand_Name__c)) {
                brandSet.add(prod.Brand_Name__c);
            }
        }

        List<String> brands = new List<String>();
        brands.addAll(brandSet);
        return brands;
    }

    public static List<ProductInfoWrapper> generateProductInfos(List<POItemInfo> poItemInfos, POInfo poData, Map<String, Product2> productMap) {
        Id priceBookId = findPriceBookForPOItem(poData.CustomerId);
        String priceBookName = '';
        for(Pricebook2 priceBook : [SELECT Name FROM Pricebook2 WHERE Id = :priceBookId]) {
            priceBookName = priceBook.Name;
        }
        List<ProductInfoWrapper> productInfoWrappers = new List<ProductInfoWrapper>();

        Set<String> prodIds = new Set<String>();
        for(POItemInfo poItemInfo : poItemInfos) {
            Product2 product = productMap.get(poItemInfo.productCode);
            if(product != null) {
                prodIds.add(product.Id);
            }
         }

        Map<String, PricebookEntry> prodEntryMap = getPriceBookEntrys(prodIds, priceBookId);

        for(POItemInfo poItemInfo : poItemInfos) {
            ProductInfoWrapper wrapper = new ProductInfoWrapper();
            Decimal productPrice = 0;
            Integer totalQuantity = 0;

            Product2 product = productMap.get(poItemInfo.productCode);
            if(product == null) {
                // CCM_PartsOrder_CheckLogic.ReturnMessage errorMessage = new CCM_PartsOrder_CheckLogic.ReturnMessage();
                // errorMessage.IsSuccess = false;
                // errorMessage.ErrorMessage = String.format('Parts Stock Number {0} not Exist, Please Correct.', new List<String>{poItemInfo.ProductCode});
                // errorMessages.add(errorMessage);   
                continue;
            }
            PricebookEntry priceBookEntry = prodEntryMap.get(product.Id);
            if(priceBookEntry == null) {
                CCM_PartsOrder_CheckLogic.ErrorDetail errDetail = new CCM_PartsOrder_CheckLogic.ErrorDetail();
                errDetail.ProductCode = poItemInfo.productCode;
                errDetail.ErrorMsg = 'Product not Added to Price Book ' + priceBookName;
                errorDetails.add(errDetail);
                continue;
            }
            wrapper.UnitPrice = PricebookEntry.UnitPrice;
            wrapper.Brand = product.Brand_Name__c;
            wrapper.ProductId = product.Id;
            wrapper.Name = product.Name;
            wrapper.ItemNumber = product.Item_Number__c;
            wrapper.ProductCode = product.ProductCode;
            wrapper.Quantity = poItemInfo.Quantity;
            wrapper.SubTotal = (PricebookEntry.UnitPrice * poItemInfo.Quantity).setScale(2);
            productInfoWrappers.add(wrapper);
        }
        return productInfoWrappers;
    }

    public static Map<String, PricebookEntry> getPriceBookEntryByProdIdWithOutConvertCurrency(Set<String> prodIds, String priceBookId){
        Map<String, PricebookEntry> prodEntryMap = new Map<String, PricebookEntry>();
        List<Pricebook2> priceBook = [
                SELECT IsStandard, Name, IsActive, Id 
                    FROM Pricebook2
                    WHERE IsStandard = false
                    AND Id =: priceBookId
                    AND IsActive = true LIMIT 1];
        if (priceBook != null && priceBook.size() > 0){
            List<PricebookEntry> priceEntries = [
                    SELECT UnitPrice,Product2Id,Name,Pricebook2Id 
                        FROM PricebookEntry
                        WHERE IsActive = true
                        AND IsDeleted = false
                        AND Pricebook2Id =: priceBook[0].Id
                        AND Product2Id IN: prodIds]; 
            for(PricebookEntry entry : priceEntries) {
                prodEntryMap.put(entry.Product2Id, entry);
            }
        }
        return prodEntryMap;
    }

    

    // Input params for creating purchase order
    public class POInfo {
        // input params
        public String CustomerNumber {get;set;}

        public String BillingAddress {get;set;}
        public String ShippingAddress {get;set;}
        public Boolean IsAlternativeAddress {get;set;}

        public String ShipBy {get;set;}

        public String CustomerPurchaseOrder {get;set;}
        public String BuyerEmail {get;set;}
        public String ShippingAndRoutingInstrcuction {get;set;}

        public List<POItemInfo> POItems {get;set;}

        // convert params
        public String CustomerId {get;set;}
        public String OrgCode {get;set;}
        public String BillingAddressId {get;set;}
        public String ShippingAddressId {get;set;}
        public Decimal ProductPrice {get;set;}
        public Integer TotalQuantity {get;set;}
        public String ShipPriority {get;set;}
    }

    // Input params for creating purchase order items
    public class POItemInfo {
        // input params
        public String ProductCode {get;set;}
        public String PartsCode {get;set;}
        public Integer Quantity {get;set;}

        // convert params
        public String Brand {get;set;}
        public Decimal UnitPrice {get;set;}
        public String ProductId {get;set;}
        public String PartsId {get;set;}
        public Decimal SubTotal {get;set;}
    }

    // Output values for product
    public class ProductInfoWrapper {
        public String ProductId {get;set;}
        public String Brand {get;set;}
        public String Name {get;set;}
        public String ItemNumber {get;set;}
        public String ProductCode {get;set;}
        public Integer Quantity {get;set;}
        public Decimal UnitPrice {get;set;}
        public Decimal SubTotal {get;set;}
    }
}