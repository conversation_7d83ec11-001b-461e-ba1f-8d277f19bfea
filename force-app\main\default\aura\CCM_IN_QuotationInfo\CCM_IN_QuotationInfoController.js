({
    doInit : function(component, event, helper) {
        component.set('v.isBusy', true);
        var stepNameList = [];
        stepNameList.push('Select Customer');
        stepNameList.push('Select Brand & Products');
        stepNameList.push('Fill in Purchase Order Information');
        stepNameList.push('Preview & Submit');

        component.set("v.stepNameList", stepNameList);

        var recordId = component.get("v.recordId");
        if(!recordId){
            recordId = helper.getUrlParameter('recordId');
        }
        component.set('v.recordId', recordId);
        if(recordId){
            // helper.getEveryPrice(component, helper);
            helper.doGetQuotationDetail(component, helper);
        }else{
            component.set("v.currentStep", 1);
        }
        component.set('v.isBusy', false);
    },
    refreshBrands : function(component, event, helper) {
        helper.doGetQuotationDetail(component, helper);
    },
    clearOrderItem: function (component, event, helper) {
        helper.doGetQuotationDetail(component, helper);
        component.set('v.quotationItems', []);
        component.set('v.quotation.Total_Quantity__c', 0);
        component.set('v.quotation.Product_Price__c', 0.00);
        component.set('v.quotation.Actual_Total_Product_Amount__c', 0.00);
        component.set('v.quotation.Discount_Amount__c', 0.00);
        component.set('v.wholeOrderPromo', "");
        component.set('v.termsPromo', "");
    }
})