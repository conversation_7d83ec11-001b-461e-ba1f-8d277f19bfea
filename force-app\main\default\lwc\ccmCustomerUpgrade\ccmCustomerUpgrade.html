<template>
    <div class="slds-card">
        <div class="slds-card__header">
            <header class="slds-text-align_center">
                <h2 class="slds-card__header-title">
                    <span class="slds-text-heading_medium">Upgrade/Merge</span>
                </h2>
            </header>
        </div>

        <div class="slds-card__body slds-card__body_inner slds-p-around_medium">
            <!-- Loading Spinner -->
            <template if:true={isProcessing}>
                <div class="slds-spinner_container">
                    <div role="status" class="slds-spinner slds-spinner_medium">
                        <span class="slds-assistive-text">Loading</span>
                        <div class="slds-spinner__dot-a"></div>
                        <div class="slds-spinner__dot-b"></div>
                    </div>
                </div>
            </template>

            <div class="slds-section slds-is-open slds-m-bottom_medium">
                <h3 class="slds-section__title">
                    <span class="slds-truncate slds-p-horizontal_small" title="Customer">
                        <lightning-icon icon-name="utility:chevrondown" size="x-small" class="slds-m-right_x-small"></lightning-icon>
                        Customer
                    </span>
                </h3>

                <!--Customer Info  -->
                <div class="slds-section__content slds-p-around_large">                      
                    <div class="slds-grid slds-gutters">                           
                        <div class="slds-col slds-size_5-of-12">                              
                            <div class="slds-form-element">
                                <div class="slds-form-element__control">
                                    <div class="slds-radio">
                                        <input type="radio"
                                            name="customerRadioOption"
                                            id="newCustomer"
                                            value="newCustomer"
                                            checked={isCreateNewCustomer}
                                            onchange={handleCustomerOptionChange} />
                                        <label class="slds-radio__label" for="newCustomer">
                                            <span class="slds-radio_faux"></span>
                                            <span class="slds-form-element__label">Create New Customer</span>
                                        </label>
                                    </div>

                                    <template if:true={isCreateNewCustomer}>
                                        <div>
                                            <lightning-input type="text"
                                                id="customerName"
                                                class="slds-input"
                                                value={originalCustomerInfo.customerName}
                                                required
                                                onchange={handleCustomerNameChange}
                                                label="Customer Name">
                                            </lightning-input>
                                            <lightning-input type="text"
                                                id="recordType"
                                                class="slds-input"
                                                disabled
                                                required
                                                value={originalCustomerInfo.recordType}
                                                onchange={handleRecordTypeChange}
                                                label="Record Type">
                                            </lightning-input>
                                            <lightning-input type="text"
                                                id="website"
                                                class="slds-input"
                                                required
                                                value={originalCustomerInfo.website}
                                                onchange={handleWebsiteChange}
                                                label="Website">
                                            </lightning-input>
                                            <lightning-dual-listbox
                                                class="slds-input" 
                                                name="languages"
                                                label="Brand"
                                                required
                                                options={options}
                                                value={originalCustomerInfo.brand}
                                                onchange={handleBrandChange}>
                                            </lightning-dual-listbox>
                                            <lightning-input-address
                                                id="shippingAddress"
                                                class="slds-input"
                                                address-label="Shipping Address"
                                                street-label="Street"
                                                street={shippingAddress.street}
                                                city-label="City"
                                                city={shippingAddress.city}
                                                country-label="Country"
                                                country={shippingAddress.country}
                                                province-label="Province"
                                                province={shippingAddress.state}
                                                postal-code-label="PostalCode"
                                                postal-code={shippingAddress.postalCode}
                                                required
                                                onchange={handleShippingAddressChange}>
                                            </lightning-input-address>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                        <div class="slds-col slds-size_2-of-12 slds-text-align_center">    
                            <span class="slds-text-body_regular slds-text-color_weak">- OR -</span>
                        </div>
                        <div class="slds-col slds-size_5-of-12">                        
                            <div class="slds-form-element">
                                <div class="slds-form-element__control">
                                    <div class="slds-radio">
                                        <input type="radio"
                                            name="customerRadioOption"
                                            id="existingCustomer"
                                            value="existingCustomer"
                                            checked={isChooseExistingCustomer}
                                            onchange={handleCustomerOptionChange} />
                                        <label class="slds-radio__label" for="existingCustomer">
                                            <span class="slds-radio_faux"></span>
                                            <span class="slds-form-element__label">Choose Existing Customer</span>
                                        </label>
                                    </div>
                                    
                                    <template if:true={isChooseExistingCustomer}>
                                        <div>
                                            <c-ccm-autocomplete
                                                id="customerselect"
                                                class="slds-input"
                                                realtime-search=true
                                                source={accountOptions}
                                                onselected={handleAccountSelected}
                                                onsearchinputchange={handleAccountInputChange}>
                                            </c-ccm-autocomplete>
                                        </div>
                                        <div class="slds-input">
                                            <lightning-input 
                                                type="text"
                                                id="existingCustomerName"
                                                value={existingCustomerInfo.Name}
                                                disabled
                                                label="Customer Name">
                                            </lightning-input>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>  
                </div>
                
                <h3 class="slds-section__title">
                    <span class="slds-truncate slds-p-horizontal_small" title="Contact">
                        <lightning-icon icon-name="utility:chevrondown" size="x-small" class="slds-m-right_x-small"></lightning-icon>
                        Contact
                    </span>
                </h3>
                <!--Contact Info  -->
                <div class="slds-section__content slds-p-around_large">                      
                    <div class="slds-grid slds-gutters">                           
                        <div class="slds-col slds-size_5-of-12">                              
                            <div class="slds-form-element">
                                <div class="slds-form-element__control">
                                    <div class="slds-radio">
                                        <input type="radio"
                                            name="contactOption"
                                            id="newContact"
                                            value="newContact"
                                            checked={isCreateNewContact}
                                            onchange={handleContactOptionChange} />
                                        <label class="slds-radio__label" for="newContact">
                                            <span class="slds-radio_faux"></span>
                                            <span class="slds-form-element__label">Create New Contact</span>
                                        </label>
                                    </div>

                                    <template if:true={isCreateNewContact}>
                                        <div>
                                            <lightning-input type="text"
                                                id="firstName"
                                                class="slds-input"
                                                value={originalContactInfo.firstName}
                                                onchange={handleFirstNameChange}
                                                label="First Name">
                                            </lightning-input>
                                            <lightning-input type="text"
                                                id="lastName"
                                                class="slds-input"
                                                required
                                                value={originalContactInfo.lastName}
                                                onchange={handleLastNameChange}
                                                label="Last Name">
                                            </lightning-input>
                                            <lightning-input type="text"
                                                id="email"
                                                class="slds-input"
                                                required
                                                value={originalContactInfo.email}
                                                onchange={handleEmailChange}
                                                label="Email">
                                            </lightning-input>
                                            <lightning-input type="text"
                                                id="phone"
                                                class="slds-input"
                                                required
                                                value={originalContactInfo.phone}
                                                onchange={handlePhoneChange}
                                                label="Phone">
                                            </lightning-input>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>

                        <div class="slds-col slds-size_2-of-12 slds-text-align_center">    
                            <span class="slds-text-body_regular slds-text-color_weak">- OR -</span>
                        </div>

                        <div class="slds-col slds-size_5-of-12">                        
                            <div class="slds-form-element">
                                <div class="slds-form-element__control">
                                    <div class="slds-radio">
                                        <input type="radio"
                                            name="contactOption"
                                            id="existingContact"
                                            value="existingContact"
                                            checked={isChooseExistingContact}
                                            onchange={handleContactOptionChange} />
                                        <label class="slds-radio__label" for="existingContact">
                                            <span class="slds-radio_faux"></span>
                                            <span class="slds-form-element__label">Choose Existing Contact</span>
                                        </label>
                                    </div>

                                    <template if:true={isChooseExistingContact}>
                                        <div>
                                            <c-ccm-autocomplete
                                                class="slds-input"
                                                realtime-search=true
                                                source={contactOptions}
                                                onselected={handleContactSelected}
                                                onsearchinputchange={handleContactInputChange}>
                                            </c-ccm-autocomplete>
                                        </div>
                                        <div class="slds-input">
                                            <lightning-input type="text"
                                                id="firstName2"
                                                value={existingContactInfo.firstName}
                                                onchange={handleFirstNameChange2}
                                                label="First Name">
                                            </lightning-input>
                                            <p class = 'slds-required'>Original Value: {referenceContactInfo.firstName}</p>
                                        </div>
                                        <div class="slds-input">
                                            <lightning-input type="text"
                                                id="lastName2"
                                                required
                                                value={existingContactInfo.lastName}
                                                onchange={handleLastNameChange2}
                                                label="Last Name">
                                            </lightning-input>
                                            <p class = 'slds-required'>Original Value: {referenceContactInfo.lastName}</p>
                                        </div>
                                        <div class="slds-input">
                                            <lightning-input type="text"
                                                id="email2"
                                                required
                                                value={existingContactInfo.email}
                                                onchange={handleEmailChange2}
                                                label="Email">
                                            </lightning-input>
                                            <p class = 'slds-required'>Original Value: {referenceContactInfo.email}</p>
                                        </div>   
                                        <div class="slds-input">
                                            <lightning-input type="text"
                                                id="phone2"
                                                required
                                                value={existingContactInfo.phone}
                                                onchange={handlePhoneChange2}
                                                label="Phone">
                                            </lightning-input>
                                            <p class = 'slds-required'>Original Value: {referenceContactInfo.phone}</p>
                                        </div> 
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>  
                </div>
            </div>

            <div class="slds-grid slds-gutters slds-p-around_medium ">
                <div class="slds-col slds-size_3-of-12">
                    <c-ccm-autocomplete
                        required
                        label="Record Owner"
                        realtime-search=true
                        source={recordOwnerOptions}
                        onselected={handleRecordOwnerSelected}
                        onsearchinputchange={handleRecordOwnerSearchInputChange}>
                    </c-ccm-autocomplete>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="slds-grid slds-gutters slds-p-around_medium">
                <div class="slds-col slds-size_1-of-1 slds-text-align_center">
                    <button class="slds-button slds-button_neutral slds-m-right_small" onclick={handleCancel}>
                        Cancel
                    </button>
                    <button class="slds-button slds-button_brand" onclick={handleUpgrade} disabled={isProcessing}>
                        Upgrade/Merge
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
