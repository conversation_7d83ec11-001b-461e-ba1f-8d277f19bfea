import { LightningElement, track, api, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import { CurrentPageReference } from 'lightning/navigation';
import { getRecord } from 'lightning/uiRecordApi';
import { IsConsoleNavigation, getFocusedTabInfo, closeTab } from 'lightning/platformWorkspaceApi';

export default class CcmSearchPageToNewCustomer extends LightningElement {

    @wire(IsConsoleNavigation) isConsoleNavigation;
    @track customerName;
    @track recordType;
    @track website;
    @track shippingAddress;
    @track recordOwner;
    
}