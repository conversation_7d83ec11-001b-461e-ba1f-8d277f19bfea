import { LightningElement, track, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import createAccount from '@salesforce/apex/CCM_SearchPageToNewCustomer.createAccount';
import getAccountRecordTypes from '@salesforce/apex/CCM_SearchPageToNewCustomer.getAccountRecordTypes';
import { IsConsoleNavigation, getFocusedTabInfo, closeTab } from 'lightning/platformWorkspaceApi';
// import getDefaultRecordType from '@salesforce/apex/CCM_SearchPageToNewCustomer.getDefaultRecordType';
// import validateWebsite from '@salesforce/apex/CCM_SearchPageToNewCustomer.validateWebsite';

export default class CcmSearchPageToNewCustomer extends NavigationMixin(LightningElement) {

    @wire(IsConsoleNavigation) isConsoleNavigation;
    @track customerName = '';
    @track recordType = '';
    @track website = '';
    @track shippingAddress = {
        street: '',
        city: '',
        state: '',
        postalCode: '',
        country: ''
    };
    @track isProcessing = false;
    @track recordTypeOptions = [];

    // Wire to get record types
    @wire(getAccountRecordTypes)
    wiredRecordTypes({ error, data }) {
        if (data) {
            this.recordTypeOptions = data;
        } else if (error) {
            this.showToast('Error', 'Failed to load record types', 'error');
        }
    }

    handleCustomerNameChange(event) {
        this.customerName = event.target.value;
    }

    handleRecordTypeChange(event) {
        this.recordType = event.target.value;
    }

    handleWebsiteChange(event) {
        this.website = event.target.value;
    }

    handleShippingAddressChange(event) {
        this.shippingAddress = {
            street: event.target.street || '',
            city: event.target.city || '',
            state: event.target.province || '',
            postalCode: event.target.postalCode || '',
            country: event.target.country || ''
        };
    }

    // Validate form
    validateForm() {
        const allValid = [...this.template.querySelectorAll('lightning-input, lightning-combobox, lightning-input-address')]
            .reduce((validSoFar, inputCmp) => {
                inputCmp.reportValidity();
                return validSoFar && inputCmp.checkValidity();
            }, true);

        if (!this.customerName) {
            this.showToast('Error', 'Customer Name is required', 'error');
            return false;
        }

        if (!this.recordType) {
            this.showToast('Error', 'Record Type is required', 'error');
            return false;
        }

        return allValid;
    }

    // async validateWebsiteUrl() {
    //     if (this.website) {
    //         try {
    //             const isValid = await validateWebsite({ website: this.website });
    //             if (!isValid) {
    //                 this.showToast('Error', 'Please enter a valid website URL (e.g., https://www.example.com)', 'error');
    //                 return false;
    //             }
    //         } catch (error) {
    //             console.error('Error validating website:', error);
    //             return false;
    //         }
    //     }
    //     return true;
    // }


    handleCancel() {
        this[NavigationMixin.Navigate]({
            type: 'standard__component',
            attributes: {
                componentName: 'c__ccmCustomerSearch'
            }
        });
    }

    async handleSave() {
        if (!this.validateForm()) {
            return;
        }

        this.isProcessing = true;

        const accountParams = {
            customerName: this.customerName,
            recordType: this.recordType,
            website: this.website,
            shippingStreet: this.shippingAddress.street,
            shippingCity: this.shippingAddress.city,
            shippingState: this.shippingAddress.state,
            shippingPostalCode: this.shippingAddress.postalCode,
            shippingCountry: this.shippingAddress.country
        };

        try {
            const result = await createAccount({ params: accountParams });

            this.isProcessing = false;

            if (result.success) {
                this.showToast('Success', result.message, 'success');

                // Navigate to the new Account record
                setTimeout(() => {
                    this[NavigationMixin.Navigate]({
                        type: 'standard__recordPage',
                        attributes: {
                            recordId: result.accountId,
                            objectApiName: 'Account',
                            actionName: 'view'
                        }
                    });
                }, 1000);
                if (this.isConsoleNavigation) {
                    const { tabId } = getFocusedTabInfo();
                    closeTab(tabId);
                }
            } else {
                this.showToast('Error', result.message, 'error');
            }
        } catch (error) {
            this.isProcessing = false;
            this.showToast('Error', 'An unexpected error occurred: ' + (error.body?.message || error.message), 'error');
        }
    }

    // Show toast message
    showToast(title, message, variant) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant
        });
        this.dispatchEvent(event);
    }
}