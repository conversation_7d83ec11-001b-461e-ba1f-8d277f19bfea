.slds-card {
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    background-color: #ffffff;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.10);
    max-width: 1000px;
    margin: 0 auto;
}

.slds-card__header {
    background: linear-gradient(135deg, #0176d3 0%, #1589ee 100%);
    border-bottom: 1px solid #d8dde6;
    padding: 1rem;
    border-radius: 0.25rem 0.25rem 0 0;
}

.slds-card__header-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.slds-card__body {
    padding: 0;
}

.slds-card__footer {
    background-color: #f7f9fb;
    border-top: 1px solid #d8dde6;
    padding: 0;
    border-radius: 0 0 0.25rem 0.25rem;
}

.slds-section__title {
    background: linear-gradient(135deg, #f7f9fb 0%, #e8f4fd 100%);
    border: 1px solid #d8dde6;
    border-bottom: none;
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-size: 0.875rem;
    color: #0176d3;
    position: relative;
}

.slds-section__title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #0176d3 0%, #1589ee 100%);
}

.slds-section__content {
    border: 1px solid #d8dde6;
    border-top: none;
    background-color: #ffffff;
}

.slds-form-element__label {
    font-weight: 500;
    font-size: 0.75rem;
    color: #3e3e3c;
    margin-bottom: 0.25rem;
}

.custom-input lightning-input,
.custom-input lightning-combobox {
    --slds-c-input-color-border: #d8dde6;
    --slds-c-input-color-border-focus: #1589ee;
    --slds-c-input-shadow-focus: 0 0 0 1px #1589ee;
    --slds-c-input-radius-border: 0.25rem;
}

.custom-input .slds-input,
.custom-input .slds-combobox__input {
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
    background-color: #ffffff;
    transition: all 0.15s ease-in-out;
}

.custom-input .slds-input:focus,
.custom-input .slds-combobox__input:focus {
    border-color: #1589ee;
    box-shadow: 0 0 0 1px #1589ee;
    outline: none;
}

.custom-input .slds-input::placeholder {
    color: #a8b2bb;
    font-style: italic;
}

.custom-address lightning-input-address {
    --slds-c-input-color-border: #d8dde6;
    --slds-c-input-color-border-focus: #1589ee;
    --slds-c-input-shadow-focus: 0 0 0 1px #1589ee;
}

.slds-spinner_container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slds-spinner {
    --slds-c-spinner-color-foreground: #0176d3;
    --slds-c-spinner-color-background: rgba(1, 118, 211, 0.1);
}

/* Section animations */
.slds-section {
    margin-bottom: 1.5rem;
    border-radius: 0.25rem;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
}

.slds-section:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.slds-section:last-child {
    margin-bottom: 0;
}

/* Form layout improvements */
.slds-form-element {
    margin-bottom: 1rem;
}

.slds-grid.slds-gutters {
    margin-left: -0.75rem;
    margin-right: -0.75rem;
}

.slds-grid.slds-gutters > .slds-col {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Custom spacing */
.slds-p-around_medium {
    padding: 1.5rem;
}

/* Required field indicator */
.slds-required {
    color: #c23934;
}